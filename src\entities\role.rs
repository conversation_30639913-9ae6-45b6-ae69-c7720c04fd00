use crate::db::{Castable, CountRecord, Database, RelateParams, WhereOptions};
use crate::{
    app_writer::AppResult,
    db::ListOptions,
    dtos::role::{RoleCreate, RoleResponse, RoleUpdate},
};
use serde::{Deserialize, Serialize};
use surrealdb::RecordId;

use super::permission::Permission;
use super::menu::Menu;
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Role {
    pub id: RecordId,
    pub name: String,
    pub code: Option<String>,
    pub desc: Option<String>,
    pub stable: bool,
    pub order: i32,
    pub permission: Option<Vec<Permission>>,
    pub menu: Option<Vec<Menu>>,
}

impl Castable for Role {}

impl Role {
    pub fn response(self) -> RoleResponse {
        RoleResponse {
            id: self.id.to_string(),
            name: self.name,
            code: self.code,
            desc: self.desc,
            stable: self.stable,
            order: self.order,
        }
    }
}

pub struct RoleBmc;

impl RoleBmc {
    const ENTITY: &'static str = "role";

    pub async fn get_list(
        page: u32,
        limit: u32,
        options: Option<ListOptions>,
        params: Vec<WhereOptions>,
    ) -> AppResult<Vec<Role>> {
        Database::exec_query_list(Self::ENTITY, page, limit, options, params).await
    }

    pub async fn get_total(params: Vec<WhereOptions>) -> AppResult<Option<CountRecord>> {
        Database::exec_query_count(Self::ENTITY, params).await
    }

    pub async fn get_by_id(id: &str) -> AppResult<Option<Role>> {
        Database::exec_get_by_id(Self::ENTITY, id).await
    }

    pub async fn create(role: RoleCreate) -> AppResult<String> {
        Database::exec_create(Self::ENTITY, role).await
    }

    pub async fn update(mut role: RoleUpdate) -> AppResult<String> {
        let tid = role.id.clone();
        let final_tid = if tid.contains(':') {
            tid.split(':').nth(1).unwrap_or(&tid).to_string()
        } else {
            tid
        };
        role.id = final_tid.clone();
        Database::exec_update(Self::ENTITY, &final_tid, role).await
    }

    pub async fn delete(id: String) -> AppResult<String> {
        Database::exec_delete(Self::ENTITY, id).await
    }

    pub async fn get_by_query(params: Vec<WhereOptions>) -> AppResult<Option<Role>> {
        // let params = vec![WhereOptions::new("rolename".to_string(), rolename)];
        Database::exec_get_by_query(Self::ENTITY, params).await
    }

    pub async fn return_item_related(from: String, to: String, item_table: String) ->  AppResult<Vec<String>> {
        Database::exec_return_relate(&item_table, &from, &to).await
    }

    pub async fn relate_to_item(params: RelateParams, state: &str) -> AppResult<String> {
        let from = vec![params.from];
        Database::exec_relate_batch(state, from, params.to).await
    }

    pub async fn get_list_by_role(
        page: u32,
        limit: u32,
        options: Option<ListOptions>,
        state: String,
        params: Vec<WhereOptions>,
    ) -> AppResult<Vec<Role>> {
        Database::exec_query_relate(Self::ENTITY, page, limit, &state, options, params).await
    }
    
    pub async fn unrelate_to_item(table: &str, from: &str, to: &str) -> AppResult<String> {
        Database::exec_unrelate(table, from, to).await
    }
}
