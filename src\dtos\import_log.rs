use crate::db::{Castable, Creatable, Patchable};
use crate::utils::date::{default_now, default_zero};
use salvo::prelude::{Extractible, ToSchema};
use serde::{Deserialize, Serialize};
use validator::Validate;

#[derive(Default, Clone, Deserialize, Serialize, Debug, Validate, ToSchema)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct ImportLogCreate {
    pub import_id: String,     // 项目ID
    pub import_source: String, // 获取数据的来源
    pub import_err: String,
    #[serde(default = "default_now")]
    pub created_at: i64,
    #[serde(default = "default_now")]
    pub updated_at: i64,
}

impl Creatable for ImportLogCreate {}

#[derive(Default, Clone, Debug, Deserialize, Serialize, Extractible, ToSchema)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct ImportLogUpdate {
    #[salvo(extract(source(from = "param")))]
    pub id: String,
    pub import_id: String,     // 项目ID
    pub import_source: String, // 获取数据的来源
    pub import_err: String,
    #[serde(default = "default_now")]
    pub created_at: i64,
    #[serde(default = "default_now")]
    pub updated_at: i64,
}

impl Patchable for ImportLogUpdate {}

#[derive(Debug, Deserialize, Serialize, ToSchema, Default, Clone)]
pub struct ImportLogResponse {
    pub id: String,
    pub import_id: String,     // 项目ID
    pub import_source: String, // 获取数据的来源
    pub import_err: String,
    pub created_at: i64,
    pub updated_at: i64,
}

impl Castable for ImportLogResponse {}
