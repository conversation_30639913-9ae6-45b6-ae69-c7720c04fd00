use super::jwt::JwtClaims;
use crate::utils::redis;
use lazy_static::lazy_static;
use regex::Regex;
use salvo::{http::StatusCode, prelude::*};

#[handler]
pub async fn check_permission(
    req: &mut Request,
    depot: &mut Depot,
    ctrl: &mut FlowCtrl,
    res: &mut Response,
) {
    let claims = depot.jwt_auth_data::<JwtClaims>().clone().unwrap();
    let claims = claims.claims.clone();
    depot.insert("current_user", claims.user_id.clone());
    depot.insert("current_role", claims.user_role.clone());
    if claims.is_admin {
        ctrl.call_next(req, depot, res).await;
    } else {
        let role_id = claims.user_role.clone();
        if role_id.is_empty() {
            ctrl.skip_rest();
            res.status_code(StatusCode::UNAUTHORIZED);
        }
        // 使用正则表达式移除路径中的动态ID部分（格式：xxx:xxxxxxxx）
        lazy_static! {
            static ref ID_PATTERN: Regex = Regex::new(r"/[^/:]+:[^/]+$").unwrap();
        }
        let raw_path = req.uri().path().to_string();
        let base_path = ID_PATTERN.replace(&raw_path, "").to_string();

        println!("base_path: {}", base_path);

        let method = req.method().to_string();
        let permit_roles: String = match redis::hget(base_path, method) {
            Ok(roles) => roles,
            Err(_) => "None".to_string(),
        };
        if &permit_roles == "None" {
            ctrl.skip_rest();
            res.status_code(StatusCode::NOT_FOUND);
        }
        let role_list = permit_roles
            .split(",")
            .map(|x| -> String { x.parse().unwrap() })
            .collect::<Vec<String>>();
        if role_list.contains(&role_id) {
            ctrl.call_next(req, depot, res).await;
        } else {
            ctrl.skip_rest();
            res.status_code(StatusCode::UNAUTHORIZED);
        }
    }
}
