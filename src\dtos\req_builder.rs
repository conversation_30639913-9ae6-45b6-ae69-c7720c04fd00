use crate::utils::date::default_now;
use salvo::prelude::{Extractible, ToSchema};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use validator::Validate;

/// 根据reqwest库的RequestBuilder构建请求，构建一个可以持久化存储的DTOs

/// 创建请求配置的DTO
#[derive(Default, Clone, Deserialize, Serialize, Debug, Validate, ToSchema)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct ReqBuilderCreate {
    /// 请求URL
    pub url: String,
    /// 请求方法 (GET, POST, PUT, DELETE等)
    pub method: String,
    /// 请求头
    pub headers: HashMap<String, String>,
    /// 查询参数
    pub query_params: HashMap<String, String>,
    /// 请求体 (JSON格式)
    pub body: Option<String>,
    /// 超时设置（秒）
    pub timeout_secs: Option<u64>,
    /// 是否启用基本认证
    pub basic_auth: Option<HashMap<String, String>>,
    /// 是否启用Bearer认证
    pub bearer_token: Option<String>,
    #[serde(default = "default_now")]
    pub created_at: i64,
    #[serde(default = "default_now")]
    pub updated_at: i64,
}

/// 更新请求配置的DTO
#[derive(Default, Clone, Debug, Deserialize, Serialize, Extractible, ToSchema)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct ReqBuilderUpdate {
    /// 请求ID
    pub id: String,
    /// 请求URL
    pub url: Option<String>,
    /// 请求方法 (GET, POST, PUT, DELETE等)
    pub method: Option<String>,
    /// 请求头
    pub headers: Option<HashMap<String, String>>,
    /// 查询参数
    pub query_params: Option<HashMap<String, String>>,
    /// 请求体 (JSON格式)
    pub body: Option<String>,
    /// 超时设置（秒）
    pub timeout_secs: Option<u64>,
    /// 是否启用基本认证
    pub basic_auth: Option<HashMap<String, String>>,
    /// 是否启用Bearer认证
    pub bearer_token: Option<String>,
    /// token是否过期
    pub token_expired: Option<bool>,
    #[serde(default = "default_now")]
    pub created_at: i64,
    #[serde(default = "default_now")]
    pub updated_at: i64,
}

/// 请求配置的响应DTO
#[derive(Default, Clone, Deserialize, Serialize, Debug, Validate, ToSchema)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct ReqBuilderResponse {
    /// 请求ID
    pub id: String,
    /// 请求URL
    pub url: String,
    /// 请求方法 (GET, POST, PUT, DELETE等)
    pub method: String,
    /// 请求头
    pub headers: Option<HashMap<String, String>>,
    /// 查询参数
    pub query_params: Option<HashMap<String, String>>,
    /// 请求体 (JSON格式)
    pub body: Option<String>,
    /// 超时设置（秒）
    pub timeout_secs: Option<u64>,
    /// 是否启用基本认证
    pub basic_auth: Option<HashMap<String, String>>,
    /// 是否启用Bearer认证
    pub bearer_token: Option<String>,
    /// token是否过期
    pub token_expired: Option<bool>,
    /// 创建时间
    pub created_at: i64,
    /// 更新时间
    pub updated_at: i64,
}
