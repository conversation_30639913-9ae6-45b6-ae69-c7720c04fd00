use crate::utils::redis;
use crate::{
    app_writer::AppWriter,
    dtos::{
        public::CaptchaResponse,
        user::{UserLoginRequest, UserLoginResponse},
    },
    services::public,
    utils::generate_captcha::create_captcha,
};
use anyhow::anyhow;
use salvo::{
    http::cookie::<PERSON><PERSON>,
    oapi::{endpoint, extract::JsonBody},
    Response, Router, Writer,
};
use uuid::Uuid;

pub fn router() -> Router {
    Router::with_path("/public")
        .push(Router::with_path("/captcha").get(get_captcha))
        .push(Router::with_path("/login").post(login))
        .push(Router::with_path("/logout").get(get_logout))
}

#[endpoint(tags("common"))]
async fn login(
    req: JsonBody<UserLoginRequest>,
    res: &mut Response,
) -> AppWriter<UserLoginResponse> {
    if req.0.captcha_str.is_empty() {
        return AppWriter(Err(anyhow!("captcha_str is required").into()));
    }
    if req.0.captcha_id.is_empty() {
        return AppWriter(Err(anyhow!("captcha_id is required").into()));
    }
    if req.0.username.is_empty() {
        return AppWriter(Err(anyhow!("username is required").into()));
    }
    let catpcha_string: String =
        redis::get(&req.0.captcha_id.clone()).unwrap_or(String::from("None"));
    if catpcha_string == "None" || catpcha_string != req.0.captcha_str.to_lowercase() {
        redis::del(&req.0.captcha_id.clone()).unwrap();
        return AppWriter(Err(anyhow!("captcha is invalid").into()));
    }
    redis::del(&req.0.captcha_id.clone()).unwrap();
    let result = public::PublicService::login(req.0).await;
    println!("登陆结果：：{:?}", result);
    match result {
        Ok(data) => {
            let jwt_token = data.token.clone();
            let cookie = Cookie::build(("cms-token", jwt_token))
                .path("/")
                .http_only(true)
                .build();
            res.add_cookie(cookie);
            return AppWriter(Ok(data));
        }
        Err(e) => return AppWriter(Err(e)),
    }
}

#[endpoint(tags("common"))]
async fn get_logout(res: &mut Response) -> AppWriter<String> {
    let cookie = Cookie::build(("jwt_token", ""))
        .path("/")
        .http_only(true)
        .build();
    res.add_cookie(cookie);
    return AppWriter(Ok("success".to_string()));
}

#[endpoint(tags("common"))]
async fn get_captcha() -> AppWriter<CaptchaResponse> {
    let (captcha_str, captcha_base64) = create_captcha();
    let uuid = Uuid::new_v4().to_string();
    // 验证码转小写
    redis::set_ex(&uuid, captcha_str.to_lowercase(), 300).unwrap();
    let res = CaptchaResponse {
        captcha_id: uuid,
        captcha_img: captcha_base64,
    };
    AppWriter(Ok(res))
}
