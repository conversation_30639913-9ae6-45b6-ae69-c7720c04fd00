use super::role::RoleService;
use crate::{
    app_writer::AppResult,
    db::{CreateParams, ListOptions, ListParams, UpdateParams, WhereOptions},
    dtos::menu::{MenuCreate, MenuUpdate},
    entities::menu::{Menu, MenuBmc},
};
use anyhow::anyhow;

pub struct MenuService;
impl MenuService {
    pub async fn get_total(req: Option<Vec<WhereOptions>>) -> AppResult<u32> {
        let mut where_options = Vec::new();
        if let Some(params) = req {
            where_options = params;
        }
        match MenuBmc::get_total(where_options).await {
            Ok(count) => {
                if let Some(count) = count {
                    Ok(count.count)
                } else {
                    Ok(0)
                }
            }
            Err(_) => return Ok(0),
        }
    }

    pub async fn get_list(req: ListParams) -> AppResult<Vec<Menu>> {
        let mut limit: u32 = 15;
        let mut offset: u32 = 0;
        if let Some(page) = req.page {
            limit = page.get_limit().unwrap_or(0);
            offset = page.get_offset();
        }
        let mut where_options = Vec::new();
        if let Some(params) = req.params {
            where_options = params;
        }
        let res = MenuBmc::get_list(offset, limit, req.options, where_options).await?;
        Ok(res)
    }

    pub async fn get_list_by_role(req: String) -> AppResult<Vec<Menu>> {
        let check = match RoleService::get_by_id(req.clone()).await {
            Ok(role) => role,
            Err(e) => return Err(e),
        };
        if check.code.is_none() {
            return Err(anyhow!("Role Code is bad").into());
        }

        let options = Some(ListOptions {
            order_by: Some("order".to_string()),
            desc: Some(false),
        });

        if check.code.unwrap() == "administrator".to_string() {
            let res = MenuBmc::get_list(0, 0, options, vec![]).await?;
            return Ok(res);
        }

        let where_options = vec![WhereOptions {
            var: "ralate".to_string(),
            val: format!("id IN {}->has_menu.out", req),
        }];
        let res = MenuBmc::get_list(0, 0, options, where_options).await?;
        Ok(res)
    }

    pub async fn get_by_query(params: Vec<WhereOptions>) -> AppResult<Option<Menu>> {
        let res = MenuBmc::get_by_query(params).await?;
        Ok(res)
    }

    pub async fn get_by_id(id: String) -> AppResult<Option<Menu>> {
        let res = MenuBmc::get_by_id(&id).await?;
        Ok(res)
    }

    pub async fn create(req: CreateParams<MenuCreate>) -> AppResult<String> {
        let check = MenuBmc::get_by_query(vec![WhereOptions::new(
            "name".to_string(),
            req.data.name.clone(),
        )])
        .await?;
        if check.is_some() {
            return Ok("Menu Name already exists".to_string());
        }
        MenuBmc::create(req.data).await?;
        Ok("Menu created".to_string())
    }

    pub async fn update(req: UpdateParams<MenuUpdate>) -> AppResult<String> {
        MenuBmc::update(req.data).await?;
        Ok("Menu updated".to_string())
    }

    pub async fn delete(id: String) -> AppResult<String> {
        MenuBmc::delete(id).await?;
        Ok("Menu deleted".to_string())
    }
}
