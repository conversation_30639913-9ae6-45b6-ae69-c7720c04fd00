use salvo::{http::StatusCode, prelude::*};
use serde::Deserialize;

#[derive(Deserialize)]
struct InitDb {
     pwd: String,
     user: String,
}

#[handler]
pub async fn init_check(
    req: &mut Request,
    depot: &mut Depot,
    ctrl: &mut FlowCtrl,
    res: &mut Response,
) {
    let req_body = req.parse_json::<InitDb>().await;
    if req_body.is_err() {
        ctrl.skip_rest();
        res.status_code(StatusCode::BAD_REQUEST);
    }
    let req_body = req_body.unwrap();
    depot.insert("init_user", req_body.user);
    depot.insert("init_pwd", req_body.pwd);
    println!("init check{:?}", depot);
    ctrl.call_next(req, depot, res).await;
}