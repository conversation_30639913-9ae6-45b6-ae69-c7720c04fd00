use crate::db::{Castable, Creatable, Patchable};
use crate::utils::date::default_now;
use crate::utils::decimal_serde::deserialize_decimal_from_float_or_string;
use rust_decimal::Decimal;
use salvo::prelude::{Extractible, ToSchema};
use serde::{Deserialize, Serialize};
use validator::Validate;

// 还款统计信息
#[derive(Default, Clone, Deserialize, Serialize, Debug, Validate, ToSchema)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct RepaymentCreate {
    pub serial: Option<String>,            // 还款计划编号
    pub contract_id: Option<String>,       // 合同ID
    pub profit_predict: Option<String>,    // 需要偿还的利润
    pub principal_predict: Option<String>, // 需要偿还的本金
    pub amount_predict: Option<String>,    // 需要偿还的总额
    pub profit_actual: Option<String>,     // 实际偿还的利润
    pub principal_actual: Option<String>,  // 需要偿还的本金
    pub amount_actual: Option<String>,     // 实际偿还的总额
    pub profit_remain: Option<String>,     // 剩余需要偿还的利润
    pub principal_remain: Option<String>,  // 需要偿还的本金
    pub amount_remain: Option<String>,     // 剩余需要偿还的总额
    pub target_amount: Option<String>,     // 目标费用总额（订单总金额）

    #[serde(deserialize_with = "deserialize_decimal_from_float_or_string")]
    #[serde(default)]
    pub profit_calc_fee: Decimal,                 // 利润计算费用
    #[serde(deserialize_with = "deserialize_decimal_from_float_or_string")]
    #[serde(default)]
    pub penalty_calc_fee: Decimal,                 // 违约金计算费用
    pub profit_calc_period: Option<String>,         // 计算周期
    pub penalty_calc_period: Option<String>, // 违约金计算方式

    pub begin_date: Option<String>,        // 额度支取的第一天，从这一天开始计算利润
    pub end_date: Option<String>,          // 约定偿还额度的最后一天，过了这一天就计算违约金
    pub repay_date: Option<String>,        // 实际还款日期
    pub remark: Option<String>,            // 备注
    pub status: Option<String>,            // 状态
    pub creator_id: Option<String>,        // 创建人ID
    pub updater_id: Option<String>,        // 更新人ID
    #[serde(default = "default_now")]
    pub created_at: i64,
    #[serde(default = "default_now")]
    pub updated_at: i64,
}

impl Creatable for RepaymentCreate {}

#[derive(Default, Clone, Debug, Deserialize, Serialize, Extractible, ToSchema)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct RepaymentUpdate {
    #[salvo(extract(source(from = "param")))]
    pub id: String,
    pub serial: Option<String>,            // 还款计划编号
    pub contract_id: Option<String>,       // 合同ID
    pub profit_predict: Option<String>,    // 需要偿还的利润
    pub principal_predict: Option<String>, // 需要偿还的本金
    pub amount_predict: Option<String>,    // 需要偿还的总额
    pub profit_actual: Option<String>,     // 实际偿还的利润
    pub principal_actual: Option<String>,  // 需要偿还的本金
    pub amount_actual: Option<String>,     // 实际偿还的总额
    pub profit_remain: Option<String>,     // 剩余需要偿还的利润
    pub principal_remain: Option<String>,  // 需要偿还的本金
    pub amount_remain: Option<String>,     // 剩余需要偿还的总额
    pub target_amount: Option<String>,     // 目标费用总额（订单总金额）

    pub profit_calc_fee: Option<Decimal>,                 // 利润计算费用
    pub penalty_calc_fee: Option<Decimal>,                 // 违约金计算费用
    pub profit_calc_period: Option<String>,         // 计算周期
    pub penalty_calc_period: Option<String>, // 违约金计算方式

    pub begin_date: Option<String>,        // 额度支取的第一天，从这一天开始计算利润
    pub end_date: Option<String>,          // 约定偿还额度的最后一天，过了这一天就计算违约金
    pub repay_date: Option<String>,        // 实际还款日期
    pub remark: Option<String>,            // 备注
    pub status: Option<String>,            // 状态
    pub creator_id: Option<String>,        // 创建人ID
    pub updater_id: Option<String>,        // 更新人ID
    #[serde(default = "default_now")]
    pub created_at: i64,
    #[serde(default = "default_now")]
    pub updated_at: i64,
}

impl Patchable for RepaymentUpdate {}

#[derive(Debug, Deserialize, Serialize, ToSchema, Default, Clone)]
pub struct RepaymentResponse {
    pub id: String,
    pub serial: Option<String>,            // 还款计划编号
    pub contract_id: Option<String>,       // 合同ID
    pub profit_predict: Option<String>,    // 需要偿还的利润
    pub principal_predict: Option<String>, // 需要偿还的本金
    pub amount_predict: Option<String>,    // 需要偿还的总额
    pub profit_actual: Option<String>,     // 实际偿还的利润
    pub principal_actual: Option<String>,  // 需要偿还的本金
    pub amount_actual: Option<String>,     // 实际偿还的总额
    pub profit_remain: Option<String>,     // 剩余需要偿还的利润
    pub principal_remain: Option<String>,  // 需要偿还的本金
    pub amount_remain: Option<String>,     // 剩余需要偿还的总额
    pub target_amount: Option<String>,     // 目标费用总额（订单总金额）

    pub profit_calc_fee: Decimal,                 // 利润计算费用
    pub penalty_calc_fee: Decimal,                 // 违约金计算费用
    pub profit_calc_period: Option<String>,         // 计算周期
    pub penalty_calc_period: Option<String>, // 违约金计算方式

    pub begin_date: Option<String>,        // 额度支取的第一天，从这一天开始计算利润
    pub end_date: Option<String>,          // 约定偿还额度的最后一天，过了这一天就计算违约金
    pub repay_date: Option<String>,        // 实际还款日期
    pub remark: Option<String>,            // 备注
    pub status: Option<String>,            // 状态
    pub creator_id: Option<String>,        // 创建人ID
    pub updater_id: Option<String>,        // 更新人ID
    pub created_at: i64,
    pub updated_at: i64,
}

impl Castable for RepaymentResponse {}

#[derive(Debug, Deserialize, Serialize, Extractible, ToSchema, Clone)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct RelateOrders {
    pub id: String,
    pub ids: Vec<String>,
}
