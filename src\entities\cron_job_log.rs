use crate::{
    app_writer::AppResult,
    db::{Castable, CountRecord, Creatable, Database, ListOptions, Patchable, WhereOptions},
};
use chrono::Local;
use serde::{Deserialize, Serialize};
use surrealdb::RecordId;

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct CronJobLog {
    pub id: Option<RecordId>,
    // 关联的定时任务ID
    pub job_id: String,
    // 任务UUID
    pub job_uuid: String,
    // 执行开始时间
    pub start_time: i64,
    // 执行结束时间
    pub end_time: i64,
    // 执行状态：success-成功，failed-失败
    pub status: String,
    // 执行结果
    pub result: Option<String>,
    // 错误信息
    pub error: Option<String>,
    pub created_at: i64,
    pub updated_at: i64,
}

impl Castable for CronJobLog {}
impl Creatable for CronJobLog {}
impl Patchable for CronJobLog {}

impl CronJobLog {
    pub fn new(job_id: String, job_uuid: String) -> Self {
        let now = Local::now().timestamp();
        Self {
            id: None,
            job_id,
            job_uuid,
            start_time: now,
            end_time: 0,
            status: "running".to_string(),
            result: None,
            error: None,
            created_at: now,
            updated_at: now,
        }
    }

    pub fn complete(mut self, result: Option<String>) -> Self {
        let now = Local::now().timestamp();
        self.end_time = now;
        self.status = "success".to_string();
        self.result = result;
        self.updated_at = now;
        self
    }

    pub fn fail(mut self, error: String) -> Self {
        let now = Local::now().timestamp();
        self.end_time = now;
        self.status = "failed".to_string();
        self.error = Some(error);
        self.updated_at = now;
        self
    }
}

pub struct CronJobLogBmc;

impl CronJobLogBmc {
    const ENTITY: &'static str = "cron_job_log";

    pub async fn get_list(
        page: u32,
        limit: u32,
        options: Option<ListOptions>,
        params: Vec<WhereOptions>,
    ) -> AppResult<Vec<CronJobLog>> {
        Database::exec_query_list(Self::ENTITY, page, limit, options, params).await
    }

    pub async fn get_total(params: Vec<WhereOptions>) -> AppResult<Option<CountRecord>> {
        Database::exec_query_count(Self::ENTITY, params).await
    }

    pub async fn create(log: CronJobLog) -> AppResult<String> {
        match Database::exec_create(Self::ENTITY, log).await {
            Ok(_) => Ok("执行记录创建成功".to_string()),
            Err(e) => Err(e),
        }
    }
}
