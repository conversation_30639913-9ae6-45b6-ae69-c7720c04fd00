use crate::{
    app_writer::AppResult,
    db::{ListParams, WhereOptions},
    dtos::req_builder::{ReqBuilderCreate, ReqBuilderUpdate},
    entities::req_builder::{Req<PERSON>uilder, ReqBuilderBmc},
};
use anyhow::anyhow;

pub struct ReqBuilderService;
impl ReqBuilderService {
    pub async fn get_total(req: Option<Vec<WhereOptions>>) -> AppResult<u32> {
        let mut where_options = Vec::new();
        if let Some(params) = req {
            where_options = params;
        }
        match ReqBuilderBmc::get_total(where_options).await {
            Ok(count) => {
                if let Some(count) = count {
                    Ok(count.count)
                } else {
                    Ok(0)
                }
            }
            Err(_) => Ok(0),
        }
    }

    pub async fn get_list(req: ListParams) -> AppResult<Vec<ReqBuilder>> {
        let mut limit: u32 = 15;
        let mut offset: u32 = 0;
        if let Some(page) = req.page {
            limit = page.get_limit().unwrap_or(0);
            offset = page.get_offset();
        }
        let mut where_options = Vec::new();
        if let Some(params) = req.params {
            where_options = params;
        }
        let res = ReqBuilderBmc::get_list(offset, limit, req.options, where_options).await?;
        Ok(res)
    }

    pub async fn get_by_query(params: Vec<WhereOptions>) -> AppResult<ReqBuilder> {
        match ReqBuilderBmc::get_by_query(params).await {
            Ok(res) => {
                if let Some(res) = res {
                    Ok(res)
                } else {
                    Err(anyhow!("ReqBuilder not found.").into())
                }
            }
            Err(e) => Err(e),
        }
    }

    pub async fn get_by_id(id: String) -> AppResult<ReqBuilder> {
        match ReqBuilderBmc::get_by_id(id).await {
            Ok(res) => {
                if let Some(res) = res {
                    Ok(res)
                } else {
                    Err(anyhow!("ReqBuilder not found.").into())
                }
            }
            Err(e) => Err(e),
        }
    }

    pub async fn create(req: ReqBuilderCreate) -> AppResult<String> {
        ReqBuilderBmc::create(req).await?;
        Ok("ReqBuilder created".to_string())
    }

    pub async fn update(req: ReqBuilderUpdate) -> AppResult<String> {
        ReqBuilderBmc::update(req).await?;
        Ok("ReqBuilder updated".to_string())
    }

    pub async fn delete(id: String) -> AppResult<String> {
        ReqBuilderBmc::delete(id).await?;
        Ok("ReqBuilder deleted".to_string())
    }
}
