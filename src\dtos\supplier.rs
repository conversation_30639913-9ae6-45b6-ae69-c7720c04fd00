use crate::db::{Castable, Creatable, Patchable};
use crate::utils::date::default_now;
use salvo::{macros::Extractible, oapi::ToSchema};
use serde::{Deserialize, Serialize};

#[derive(Default, Debug, Deserialize, Serialize, Extractible, ToSchema)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct SupplierCreate {
    pub status: Option<String>,
    pub serial: String,
    pub name: String,
    pub address: Option<String>,
    pub website: Option<String>,
    pub image: Option<String>,
    #[serde(default = "default_now")]
    pub created_at: i64,
    #[serde(default = "default_now")]
    pub updated_at: i64,
}

impl Creatable for SupplierCreate {}

#[derive(Default, Debug, Deserialize, Serialize, Extractible, ToSchema)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct SupplierUpdate {
    pub id: String,
    pub status: Option<String>,
    pub serial: String,
    pub name: String,
    pub address: Option<String>,
    pub website: Option<String>,
    pub image: Option<String>,
    #[serde(default = "default_now")]
    pub created_at: i64,
    #[serde(default = "default_now")]
    pub updated_at: i64,
}

impl Patchable for SupplierUpdate {}

#[derive(Default, Debug, Deserialize, Serialize, Extractible, ToSchema)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct SupplierResponse {
    pub id: String,
    pub status: Option<String>,
    pub serial: String,
    pub name: String,
    pub address: Option<String>,
    pub website: Option<String>,
    pub image: Option<String>,
    pub created_at: i64,
    pub updated_at: i64,
}
impl Castable for SupplierResponse {}
