use std::str::FromStr;
use std::{ffi::OsStr, path::Path};

use crate::app_writer::AppResult;
use crate::dtos::upload::{MultiResult, SingleResult};
use crate::utils::os_path::md5_string;
use crate::utils::rand_utils::random_string;
use crate::CFG;
use anyhow::anyhow;
use qcos::client::Client;
use qcos::objects::mime;
use qcos::request::{ErrNo, Request, Response};
use reqwest::header::{HeaderName, HeaderValue};

use reqwest::Body;
use salvo::http::form::FilePart;

use super::UploadMethod;

pub struct Tencent;

impl UploadMethod for Tencent {
    async fn add_single(&self, req: FilePart, target: &str) -> AppResult<SingleResult> {
        let file = req;
        let client = init_client();

        let filename = file
            .name()
            .unwrap_or_default()
            .trim_start_matches('/')
            .to_owned();
        let path = Path::new(&filename);
        let stem = path.file_stem().and_then(OsStr::to_str).unwrap_or_default();
        let ext = path
            .extension()
            .and_then(OsStr::to_str)
            .unwrap_or_default()
            .to_lowercase();
        let new_name = format!("{}-{}.{}", stem, random_string(10), ext.clone());

        // 构建目标路径
        let dest_path = Path::new(&target).join(&new_name);
        let upload_path = dest_path.clone().to_str().unwrap().to_string();
        let md5digest = md5_string(new_name.clone());

        // 使用腾讯云COS上传文件
        let file_content =
            std::fs::read(file.path()).map_err(|e| anyhow!("文件读取失败: {}", e))?;
        let resp = client
            .put_object_binary(
                file_content,
                &upload_path,
                Some(mime::TEXT_PLAIN_UTF_8),
                None,
            )
            .await;
        if resp.error_no != ErrNo::SUCCESS {
            return Err(anyhow!("上传失败!{}::{}", resp.error_no, resp.error_message).into());
        }

        let res = SingleResult {
            filename: new_name,
            file_type: ext,
            path: target.to_string(),
            url: cos_url(&dest_path),
            md5: Some(md5digest),
        };
        Ok(res)
    }

    async fn add_multi(&self, req: Vec<FilePart>, target: &str) -> AppResult<MultiResult> {
        let mut res = MultiResult {
            path: target.to_string(),
            files: vec![],
        };

        let client = init_client();

        for file in req {
            let filename = file
                .name()
                .unwrap_or_default()
                .trim_start_matches('/')
                .to_owned();
            let path = Path::new(&filename);
            let stem = path.file_stem().and_then(OsStr::to_str).unwrap_or_default();
            let ext = path
                .extension()
                .and_then(OsStr::to_str)
                .unwrap_or_default()
                .to_lowercase();
            let new_name = format!("{}-{}.{}", stem, random_string(10), ext.clone());

            let dest_path = Path::new(&target).join(&new_name);
            let upload_path = dest_path.clone().to_str().unwrap().to_string();
            let md5digest = md5_string(new_name.clone());

            // 上传到腾讯云COS
            let file_content =
                std::fs::read(file.path()).map_err(|e| anyhow!("文件读取失败: {}", e))?;
            let resp = client
                .put_object_binary(
                    file_content,
                    &upload_path,
                    Some(mime::TEXT_PLAIN_UTF_8),
                    None,
                )
                .await;
            if resp.error_no != ErrNo::SUCCESS {
                return Err(anyhow!("上传失败!{}::{}", resp.error_no, resp.error_message).into());
            }

            res.files.push(SingleResult {
                filename: new_name,
                file_type: ext,
                path: target.to_string(),
                url: cos_url(&dest_path),
                md5: Some(md5digest),
            });
        }

        Ok(res)
    }

    async fn delete(&self, target: &str) -> AppResult<String> {
        let client = init_client();
        let resp = client.delete_object(target).await;
        if resp.error_no != ErrNo::SUCCESS {
            return Err(anyhow!("删除失败!{}::{}", resp.error_no, resp.error_message).into());
        }
        Ok("删除成功".to_string())
    }

    async fn make_permanent(&self, source: &str) -> AppResult<SingleResult> {
        let client = init_client();

        // 检查文件是否存在
        let resp = client.get_object_size(source).await;
        if resp == -1 {
            return Err(anyhow!("源文件不存在").into());
        }

        // 检查是否是tmp开头
        if !source.starts_with("tmp/") {
            return Err(anyhow!("源文件路径必须以tmp/开头").into());
        }

        // 构建目标路径
        let target = source.replacen("tmp/", "", 1);

        // 执行移动操作
        let resp = copy_object(&client, source, &target).await?;
        if resp.error_no != ErrNo::SUCCESS {
            return Err(anyhow!("移动文件失败!{}::{}", resp.error_no, resp.error_message).into());
        }

        let resp = client.delete_object(source).await;
        if resp.error_no != ErrNo::SUCCESS {
            return Err(anyhow!("删除源文件失败!{}::{}", resp.error_no, resp.error_message).into());
        }
        let res = SingleResult {
            filename: "".to_string(),
            file_type: "".to_string(),
            path: target.clone().to_string(),
            url: cos_url(&Path::new(&target)),
            md5: None,
        };
        Ok(res)
    }
}

// 初始化腾讯云COS客户端
fn init_client() -> Client {
    Client::new(
        CFG.oss.secret_id.clone(),
        CFG.oss.secret_key.clone(),
        CFG.oss.bucket_name.clone(),
        CFG.oss.region.clone(),
    )
}

/// 复制对象
/// # 参数
/// - source: 源文件路径
/// - target: 目标文件路径
async fn copy_object(
    client: &Client,
    source: &str,
    target: &str,
) -> Result<Response, anyhow::Error> {
    let mut headers = client.get_common_headers();

    // 添加源文件路径到header，需要包含bucket名称
    headers.insert(
        HeaderName::from_str("x-cos-copy-source").unwrap(),
        HeaderValue::from_str(&format!("{}/{}", CFG.oss.bucket_name, source)).unwrap(),
    );

    let url_path = client.get_path_from_object_key(target);
    let headers = client.get_headers_with_auth("put", url_path.as_str(), None, Some(headers), None);

    let resp = Request::put(
        client.get_full_url_from_path(url_path.as_str()).as_str(),
        None,
        Some(&headers),
        None,
        None,
        None as Option<Body>,
    )
    .await;

    match resp {
        Ok(response) => Ok(response),
        Err(e) => Err(anyhow!("复制文件失败: {}", e.error_message)),
    }
}

// 构建腾讯云COS的URL
fn cos_url(dest: &Path) -> String {
    let key = dest.to_str().unwrap();
    format!(
        "https://{}.cos.{}.myqcloud.com/{}",
        CFG.oss.bucket_name, CFG.oss.region, key
    )
}
