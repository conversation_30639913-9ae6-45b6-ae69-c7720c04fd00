use crate::{
    app_writer::{AppR<PERSON>ult, AppWriter},
    db::{ListOptions, ListParams, ListResponse},
    dtos::repayment::{RepaymentCreate, RepaymentResponse, RepaymentUpdate},
    services::repayment::RepaymentService,
};
use salvo::{
    oapi::{
        endpoint,
        extract::{JsonBody, PathParam},
    },
    Request,
};
use salvo::{Router, Writer};

pub fn router() -> Router {
    Router::with_path("repayment")
        .post(create_repayment)
        .put(update_repayment)
        .push(
            Router::with_path("<id>")
                .get(get_repayment_by_id)
                .delete(delete_repayment),
        )
        .push(Router::with_path("list").post(get_repayment_list))
}

#[endpoint(tags("repayment"))]
async fn get_repayment_list(
    req: JsonBody<ListParams>,
) -> AppWriter<ListResponse<RepaymentResponse>> {
    let list = RepaymentService::get_list(req.0.clone()).await.unwrap();

    let mut data: Vec<RepaymentResponse> = Vec::new();
    for item in list {
        let tmp = item.response();
        data.push(tmp);
    }
    let page = req.0.page.clone().unwrap();
    let total = RepaymentService::get_total(req.params.clone())
        .await
        .unwrap();
    let res = ListResponse {
        data,
        total,
        page: page.page,
        size: page.limit,
    };

    AppWriter(Ok(res))
}

#[endpoint(tags("repayment"), parameters(("id", description = "repayment id for params")))]
async fn get_repayment_by_id(id: PathParam<String>) -> AppWriter<RepaymentResponse> {
    match RepaymentService::get_by_id(id.0).await {
        Ok(repayment) => {
            let res = repayment.response();
            AppWriter(Ok(res))
        }
        Err(e) => AppWriter(Err(e)),
    }
}

#[endpoint(tags("repayment"))]
async fn create_repayment(req: JsonBody<RepaymentCreate>) -> AppWriter<String> {
    let result = RepaymentService::create(req.0).await;
    AppWriter(result)
}

#[endpoint(tags("repayment"))]
async fn update_repayment(req: JsonBody<RepaymentUpdate>) -> AppResult<AppWriter<String>> {
    let result = RepaymentService::update(req.0).await;
    Ok(AppWriter(result))
}

#[endpoint(tags("repayment"), parameters(("id", description = "repayment id")))]
async fn delete_repayment(id: PathParam<String>) -> AppWriter<String> {
    let result = RepaymentService::delete(id.0).await;
    AppWriter(result)
}