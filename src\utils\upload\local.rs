use std::{ffi::OsStr, fs, path::Path};

use crate::app_writer::AppResult;
use crate::dtos::upload::{MultiResult, SingleResult};
use crate::utils::os_path::md5_string;
use crate::utils::rand_utils::random_string;
use anyhow::anyhow;
use salvo::http::form::FilePart;

use super::UploadMethod;

pub struct Local;

impl UploadMethod for Local {
    async fn add_single(&self, req: FilePart, target: &str) -> AppResult<SingleResult> {
        let file = req;
        let filename = file
            .name()
            .unwrap_or_default()
            .trim_start_matches('/')
            .to_owned();
        let path = Path::new(&filename);
        let stem = path.file_stem().and_then(OsStr::to_str).unwrap_or_default();
        let ext = path
            .extension()
            .and_then(OsStr::to_str)
            .unwrap_or_default()
            .to_lowercase();
        let new_name = format!("{}-{}.{}", stem, random_string(10), ext.clone());
        let dest = Path::new(&target).join(&new_name);
        println!("{:?}", &target);
        fs::create_dir_all(&target).ok();
        fs::copy(&file.path(), &dest).map_err(|e| anyhow!("文件复制失败: {}", e))?;
        let md5digest = md5_string(new_name.clone());

        // let check_dir = crate::os_path::check_path(format!("{}{}", CFG.web.upload_url, &target));

        let res = SingleResult {
            filename: new_name,
            file_type: ext,
            path: dest.clone().into_os_string().into_string().unwrap(),
            url: format!(
                "{}{}",
                String::from("sys-upload:/"),
                dest.into_os_string().into_string().unwrap()
            ),
            md5: Some(md5digest),
        };
        Ok(res)
    }

    async fn add_multi(&self, req: Vec<FilePart>, target: &str) -> AppResult<MultiResult> {
        let mut res = MultiResult {
            path: target.to_string(),
            files: vec![],
        };

        for file in req {
            let filename = file
                .name()
                .unwrap_or_default()
                .trim_start_matches('/')
                .to_owned();
            let path = Path::new(&filename);
            let stem = path.file_stem().and_then(OsStr::to_str).unwrap_or_default();
            let ext = path
                .extension()
                .and_then(OsStr::to_str)
                .unwrap_or_default()
                .to_lowercase();
            let new_name = format!("{}-{}.{}", stem, random_string(10), ext.clone());
            let dest = Path::new(&target).join(&new_name);
            fs::create_dir_all(&target).ok();
            fs::copy(&file.path(), &dest).map_err(|e| anyhow!("文件复制失败: {}", e))?;
            let md5digest = md5_string(new_name.clone());
            res.files.push(SingleResult {
                filename: new_name,
                file_type: ext,
                path: dest.clone().into_os_string().into_string().unwrap(),
                url: format!(
                    "{}{}",
                    String::from("sys-upload:/"),
                    dest.into_os_string().into_string().unwrap()
                ),
                md5: Some(md5digest),
            });
        }
        Ok(res)
    }

    async fn delete(&self, target: &str) -> AppResult<String> {
        if Path::new(&target).exists() {
            fs::remove_file(target).map_err(|e| anyhow!("删除失败: {}", e))?
        }
        Ok("删除成功".to_string())
    }

    async fn make_permanent(&self, source: &str) -> AppResult<SingleResult> {
        let source_path = Path::new(source);

        // 检查文件是否存在
        if !source_path.exists() {
            return Err(anyhow!("源文件不存在").into());
        }

        // 检查是否是tmp开头
        if !source.starts_with("tmp/") {
            return Err(anyhow!("源文件路径必须以tmp/开头").into());
        }

        // 构建目标路径
        let target = source.replacen("tmp/", "", 1);
        let target_path = Path::new(&target);

        // 确保目标目录存在
        if let Some(parent) = target_path.parent() {
            fs::create_dir_all(parent).map_err(|e| anyhow!("创建目标目录失败: {}", e))?;
        }

        // 执行移动操作
        fs::rename(source_path, target_path).map_err(|e| anyhow!("移动文件失败: {}", e))?;
        let res = SingleResult {
            filename: "".to_string(),
            file_type: "".to_string(),
            path: target.clone().to_string(),
            url: format!("sys-upload:/{}", target),
            md5: None,
        };
        Ok(res)
    }
}
