# 实体层优化：将字段处理逻辑从BMC层移至实体层

## 概述

本次重构将复杂的字段处理逻辑从BMC层移至实体层，通过在`impl SalesOrder`中添加`to_update_options()`方法，实现了更好的代码组织和管理。

## 重构前后对比

### 重构前：BMC层臃肿

```rust
// SalesOrderBmc::update_optimized() - 130多行代码
pub async fn update_optimized(sales_order: SalesOrderUpdate) -> AppResult<String> {
    // 检查记录存在性...
    
    // 构建更新字段列表 - 大量重复代码
    let mut update_fields = Vec::new();
    if let Some(status) = &sales_order.status {
        update_fields.push(UpdateOptions::new("status".to_string(), status.clone()));
    }
    if let Some(creator_id) = &sales_order.creator_id {
        update_fields.push(UpdateOptions::new("creator_id".to_string(), creator_id.clone()));
    }
    // ... 30多个字段的重复处理
    
    // 执行更新...
}
```

### 重构后：职责分离

#### 实体层 (SalesOrder)
```rust
/// 将 SalesOrderUpdate 转换为 UpdateOptions 列表
pub fn to_update_options(update_data: SalesOrderUpdate) -> Vec<UpdateOptions> {
    let mut update_fields = Vec::new();
    let time_now = Local::now().timestamp_millis();
    
    // 可选字段处理
    if let Some(status) = update_data.status {
        update_fields.push(UpdateOptions::new("status".to_string(), status));
    }
    // ... 其他字段处理
    
    // 必需字段处理
    update_fields.push(UpdateOptions::new("serial".to_string(), update_data.serial));
    // ... 数值字段处理
    
    update_fields
}
```

#### BMC层 (SalesOrderBmc)
```rust
/// 优化版本的更新方法 - 简洁明了
pub async fn update_optimized(sales_order: SalesOrderUpdate) -> AppResult<String> {
    // 检查记录是否存在
    let check: Option<SalesOrder> =
        Database::exec_get_by_id(Self::ENTITY, &sales_order.id.clone()).await?;
    if check.is_none() {
        return Err(AppError::AnyHow(anyhow!("SalesOrder not found.")));
    }

    // 构建查询条件
    let params = vec![WhereOptions::new("id".to_string(), sales_order.id.clone())];
    
    // 使用实体层的方法转换为更新字段列表
    let update_fields = SalesOrder::to_update_options(sales_order);
    
    // 执行多字段更新
    Self::update_multiple_fields(params, update_fields).await
}
```

## 优化效果

### 1. 代码组织改进
- **BMC层**：从130多行减少到15行，专注于数据库操作逻辑
- **实体层**：承担字段转换责任，符合领域驱动设计原则
- **职责分离**：每层都有明确的职责边界

### 2. 可维护性提升
- **集中管理**：字段处理逻辑集中在实体层
- **易于扩展**：新增字段只需在实体层修改
- **减少重复**：避免在多个地方重复字段处理逻辑

### 3. 可复用性增强
- **模式复用**：相同模式可应用到其他实体
- **方法复用**：`to_update_options`方法可在不同场景下使用
- **逻辑复用**：字段处理逻辑可被其他更新方法复用

## 设计模式

### 实体层职责
```rust
impl SalesOrder {
    // 传统的完整对象更新
    pub fn update(new: SalesOrderUpdate, old: SalesOrder) -> SalesOrder { ... }
    
    // 新增：字段级更新转换
    pub fn to_update_options(update_data: SalesOrderUpdate) -> Vec<UpdateOptions> { ... }
}
```

### BMC层职责
```rust
impl SalesOrderBmc {
    // 传统方法：完整对象更新
    pub async fn update(sales_order: SalesOrderUpdate) -> AppResult<String> { ... }
    
    // 优化方法：多字段更新
    pub async fn update_optimized(sales_order: SalesOrderUpdate) -> AppResult<String> { ... }
    
    // 基础方法：单字段更新
    pub async fn update_field(params: Vec<WhereOptions>, field: &str, value: &str) -> AppResult<String> { ... }
    
    // 基础方法：多字段更新
    pub async fn update_multiple_fields(params: Vec<WhereOptions>, fields: Vec<UpdateOptions>) -> AppResult<String> { ... }
}
```

## 扩展示例：SalesOrderInfo

应用相同的模式到其他实体：

```rust
impl SalesOrderInfo {
    pub fn to_update_options(update_data: SalesOrderInfoUpdate) -> Vec<UpdateOptions> {
        let mut update_fields = Vec::new();
        let time_now = Local::now().timestamp_millis();
        
        // 必需字段
        update_fields.push(UpdateOptions::new("order_serial".to_string(), update_data.order_serial));
        update_fields.push(UpdateOptions::new("product_serial".to_string(), update_data.product_serial));
        
        // 可选字段
        if let Some(product_name) = update_data.product_name {
            update_fields.push(UpdateOptions::new("product_name".to_string(), product_name));
        }
        
        // 数值字段
        update_fields.push(UpdateOptions::new("sales_price".to_string(), update_data.sales_price.to_string()));
        
        // 自动更新时间戳
        update_fields.push(UpdateOptions::new("updated_at".to_string(), time_now.to_string()));
        
        update_fields
    }
}
```

## 使用指南

### 1. 选择合适的更新方法

```rust
// 传统方法 - 适用于复杂业务逻辑
let result = SalesOrderService::update(sales_order_update).await?;

// 优化方法 - 适用于性能敏感场景
let result = SalesOrderService::update_optimized(sales_order_update).await?;

// 单字段更新 - 适用于简单字段更新
let result = SalesOrderService::update_field(params, "status", "completed").await?;

// 多字段更新 - 适用于自定义字段组合
let update_fields = vec![
    UpdateOptions::new("status".to_string(), "completed".to_string()),
    UpdateOptions::new("amount".to_string(), "1500.00".to_string()),
];
let result = SalesOrderService::update_multiple_fields(params, update_fields).await?;
```

### 2. 为新实体添加优化支持

1. 在实体的`impl`块中添加`to_update_options`方法
2. 在BMC的`impl`块中添加`update_optimized`方法
3. 在Service层添加对应的包装方法

## 总结

通过将字段处理逻辑从BMC层移至实体层，我们实现了：

- **更好的代码组织**：每层职责明确
- **更高的可维护性**：逻辑集中管理
- **更强的可扩展性**：模式易于复用
- **更优的性能**：保持多字段更新的性能优势
- **更好的设计**：符合领域驱动设计原则

这种重构不仅解决了代码臃肿问题，还为未来的扩展奠定了良好的基础。
