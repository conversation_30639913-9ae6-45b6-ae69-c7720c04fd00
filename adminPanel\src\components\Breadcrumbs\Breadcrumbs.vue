<template>
  <q-breadcrumbs class="flex items-center" active-color="none">
    <transition-group appear enter-active-class="animated fadeInRight">
      <template v-for="(breadcrumb, index) in breadcrumbsStore.getBreadCrumbs">
        <q-breadcrumbs-el v-if="breadcrumb.title" name="breadcrumb" :label="checkTitle(breadcrumb.title)"
          :icon="showIcon ? breadcrumb.icon : undefined" :key="index + breadcrumb.title">
          <div v-if="breadcrumbsStore.getBreadCrumbs.length !== index + 1" name="breadcrumb"
            style="margin: 0px 0px 0px 8px">
            /
          </div>
        </q-breadcrumbs-el>
      </template>
    </transition-group>
  </q-breadcrumbs>
</template>

<script lang="ts" setup>
import { useBreadcrumbsStore } from "src/stores/breadcrumbs";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
interface Props {
  showIcon?: boolean;
}

defineOptions({ name: "Breadcrumbs" });

withDefaults(defineProps<Props>(), { showIcon: true });

const breadcrumbsStore = useBreadcrumbsStore();

const checkTitle = (item: string) => {
  const string = item.split("：");
  if (string.length < 2) {
    return t("admin." + item);
  } else {
    return t("admin." + string[0]) + "：" + string[1];
  }
};
</script>
