use crate::{
    app_writer::AppResult,
    db::{ListParams, RelateParams, WhereOptions},
    dtos::attachment::{AttachmentCreate, AttachmentUpdate},
    entities::attachment::{Attachment, AttachmentBmc},
};
use anyhow::anyhow;

pub struct AttachmentService;
impl AttachmentService {
    pub async fn get_total(req: Option<Vec<WhereOptions>>) -> AppResult<u32> {
        let mut where_options = Vec::new();
        if let Some(params) = req {
            where_options = params;
        }
        match AttachmentBmc::get_total(where_options).await {
            Ok(count) => {
                if let Some(count) = count {
                    Ok(count.count)
                } else {
                    Ok(0)
                }
            }
            Err(_) => Ok(0),
        }
    }

    pub async fn get_list(req: ListParams) -> AppResult<Vec<Attachment>> {
        let mut limit: u32 = 15;
        let mut offset: u32 = 0;
        if let Some(page) = req.page {
            limit = page.get_limit().unwrap_or(0);
            offset = page.get_offset();
        }
        let mut where_options = Vec::new();
        if let Some(params) = req.params {
            where_options = params;
        }
        let res = AttachmentBmc::get_list(offset, limit, req.options, where_options).await?;
        Ok(res)
    }

    pub async fn get_by_query(params: Vec<WhereOptions>) -> AppResult<Attachment> {
        match AttachmentBmc::get_by_query(params).await {
            Ok(res) => {
                if let Some(res) = res {
                    Ok(res)
                } else {
                    Err(anyhow!("Attachment not found.").into())
                }
            }
            Err(e) => Err(e),
        }
    }

    pub async fn get_by_id(id: String) -> AppResult<Attachment> {
        match AttachmentBmc::get_by_id(&id).await {
            Ok(res) => {
                if let Some(res) = res {
                    Ok(res)
                } else {
                    Err(anyhow!("Attachment not found.").into())
                }
            }
            Err(e) => Err(e),
        }
    }

    pub async fn create(req: AttachmentCreate) -> AppResult<String> {
        AttachmentBmc::create(req).await?;
        Ok("Attachment created".to_string())
    }

    pub async fn update(req: AttachmentUpdate) -> AppResult<String> {
        AttachmentBmc::update(req).await?;
        Ok("Attachment updated".to_string())
    }

    pub async fn delete(id: String) -> AppResult<String> {
        AttachmentBmc::delete(id).await?;
        Ok("Attachment deleted".to_string())
    }

    pub async fn relate_to(params: RelateParams, state: &str) -> AppResult<String> {
        AttachmentBmc::relate_to_item(params, state).await?;
        Ok("Attachment related".to_string())
    }
}
