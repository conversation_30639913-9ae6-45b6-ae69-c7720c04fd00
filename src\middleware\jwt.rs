use anyhow::Result;
use jsonwebtoken::{decode, Algor<PERSON>m, <PERSON><PERSON><PERSON><PERSON><PERSON>, Enco<PERSON><PERSON>ey, Valida<PERSON>};
use salvo::jwt_auth::{ConstDecoder, <PERSON>ie<PERSON>inder, HeaderFinder, QueryFinder};
use salvo::prelude::*;
use serde::{Deserialize, Serialize};
use time::{Duration, OffsetDateTime};

use crate::config::CFG;
use crate::dtos::user::UserResponse;
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct JwtClaims {
    pub username: String,
    pub user_id: String,
    pub user_role: String,
    pub user_company: String,
    pub is_admin: bool,
    pub exp: i64,
    pub buf: i64,
}

#[allow(dead_code)]
pub fn jwt_middleware() -> JwtAuth<JwtClaims, ConstDecoder> {
    let auth_handler: JwtAuth<JwtClaims, _> = JwtAuth::new(ConstDecoder::from_secret(
        CFG.jwt.jwt_secret.to_owned().as_bytes(),
    ))
    .finders(vec![
        Box::new(HeaderFinder::new()),
        Box::new(QueryFinder::new("token")),
        Box::new(CookieFinder::new("jwt_token")),
    ])
    .force_passed(false);
    auth_handler
}

#[allow(dead_code)]
pub fn get_token(user: UserResponse) -> Result<(String, i64)> {
    let exp = OffsetDateTime::now_utc() + Duration::seconds(CFG.jwt.jwt_exp);
    let claim = JwtClaims {
        username: user.login_name,
        user_id: user.id.to_string(),
        user_role: user.role_id.unwrap_or("".to_string()).to_string(),
        user_company: user.company_id.unwrap_or("".to_string()),
        is_admin: user.is_admin,
        exp: exp.unix_timestamp(),
        buf: CFG.jwt.jwt_buf,
    };
    let token: String = jsonwebtoken::encode(
        &jsonwebtoken::Header::default(),
        &claim,
        &EncodingKey::from_secret(CFG.jwt.jwt_secret.as_bytes()),
    )?;
    Ok((token, exp.unix_timestamp()))
}

#[allow(dead_code)]
pub fn decode_token(token: &str) -> bool {
    let validation = Validation::new(Algorithm::HS256);
    decode::<JwtClaims>(
        token,
        &DecodingKey::from_secret(CFG.jwt.jwt_secret.as_bytes()),
        &validation,
    )
    .is_ok()
}
