<template>
  <BaseContent scrollable>
    <div class="row q-ma-md">
      <div class="col">
        <q-table v-model:pagination="pagination" row-key="id" separator="cell" :rows="tableData" :columns="columns"
          :rows-per-page-options="pageOptions" :loading="loading" @request="onRequest">
          <template #top="props">

            <!-- 订单搜索过滤器 -->
            <div class="row q-gutter-md items-end">
              <div class="col-auto" style="min-width: 200px;">
                <q-input v-model="searchParams.platform_order_serial" outlined dense clearable label="平台订单编号"
                  placeholder="请输入平台订单编号" @keyup.enter="handleSearch" />
              </div>
              <div class="col-auto" style="min-width: 200px;">
                <q-input v-model="searchParams.serial" outlined dense clearable label="订单编号" placeholder="请输入订单编号"
                  @keyup.enter="handleSearch" />
              </div>
              <div class="col-auto" style="min-width: 200px;">
                <q-input v-model="searchParams.platform_name" outlined dense clearable label="销售平台"
                  placeholder="请输入销售平台" @keyup.enter="handleSearch" />
              </div>
              <div class="col-auto" style="min-width: 150px;">
                <q-input v-model="searchParams.status" outlined dense clearable label="订单状态" placeholder="请选择订单状态"
                  @keyup.enter="handleSearch" />
              </div>
              <div class="col-auto">
                <div class="row q-gutter-sm">
                  <q-btn color="primary" label="搜索" icon="search" @click="handleSearch" />
                  <q-btn color="grey" label="重置" icon="refresh" @click="resetSearch" />
                </div>
              </div>
            </div>
            <q-space />
            <q-btn flat round dense :icon="props.inFullscreen ? 'fullscreen_exit' : 'fullscreen'" class="q-ml-md"
              @click="props.toggleFullscreen" />
          </template>

          <template #body-cell-status="props">
            <q-td :props="props">
              <q-chip v-if="props.row.status === 'success'" square outline size="sm" color="primary" label="成功" />
              <q-chip v-else square outline size="sm" label="失败" />
            </q-td>
          </template>

          <template #body-cell-actions="props">
            <q-td :props="props">
              <div class="q-gutter-xs">
                <q-btn-group>
                  <q-btn color="primary" :label="t('admin.Edit')" size="sm" @click="showEditForm(props.row)" />
                  <q-btn color="secondary" :label="t('admin.Detail')" size="sm" @click="handleDetail(props.row)" />
                  <q-btn v-if="!props.row.stable" color="negative" :label="t('admin.Delete')" size="sm"
                    @click="handleDelete(props.row)" />
                </q-btn-group>
              </div>
            </q-td>
          </template>
        </q-table>
      </div>
    </div>
    <RecordImport ref="recordDetailDialog" @handleFinish="handleFinish" />
  </BaseContent>
</template>

<script setup>
import { useQuasar } from "quasar";
import BaseContent from "src/components/BaseContent/BaseContent.vue";
import useTableData from "src/composables/useTableData";
import { computed, onMounted, ref, watch } from "vue";
import { useI18n } from "vue-i18n";
import { useRouter } from "vue-router";
import RecordImport from "./modules/recordImport.vue";

const router = useRouter();
const $q = useQuasar();
const { t } = useI18n();
const url = {
  list: "/api/sales_order/list",
  create: "/api/sales_order",
  edit: "/api/sales_order",
  delete: "/api/sales_order",
};
const columns = computed(() => {
  return [
    {
      name: "platform_order_serial",
      align: "center",
      label: "平台订单编号",
      field: "platform_order_serial",
    },
    {
      name: "platform_name",
      align: "center",
      label: "销售平台",
      field: "platform_name",
    },
    {
      name: "serial",
      required: true,
      align: "center",
      label: "订单编号",
      field: "serial",
    },
    {
      name: "total_payment",
      required: true,
      align: "center",
      label: "订单总额",
      field: "total_payment",
    },
    {
      name: "purchase_time",
      required: true,
      align: "center",
      label: "下单时间",
      field: "purchase_time",
    },
    {
      name: "pay_time",
      required: true,
      align: "center",
      label: "支付时间",
      field: "pay_time",
    },
    {
      name: "delivery_time",
      required: true,
      align: "center",
      label: "发货时间",
      field: "delivery_time",
    },
    {
      name: "sign_time",
      required: true,
      align: "center",
      label: "签收时间",
      field: "sign_time",
    },
    {
      name: "complete_time",
      required: true,
      align: "center",
      label: "完成时间",
      field: "complete_time",
    },
    {
      name: "status",
      align: "center",
      label: "订单状态",
      field: "status",
    },
    {
      name: "actions",
      required: true,
      align: "center",
      label: "操作",
      field: "actions",
    },
  ];
});
const {
  pagination,
  queryParams,
  pageOptions,
  loading,
  tableData,
  recordDetailDialog,
  onRequest,
  getTableData,
  showAddForm,
  showEditForm,
  handleDelete,
  handleFinish,
} = useTableData(url);


// 搜索参数
const searchParams = ref({
  platform_order_serial: '',
  serial: '',
  platform_name: '',
  status: '',
});

// 搜索功能
const handleSearch = () => {
  // 过滤空值，只传递有值的搜索参数
  const filteredParams = {};
  Object.keys(searchParams.value).forEach(key => {
    if (searchParams.value[key] && searchParams.value[key].trim() !== '') {
      filteredParams[key] = searchParams.value[key].trim();
    }
  });
  queryParams.value = filteredParams;
  getTableData();
};

// 重置搜索
const resetSearch = () => {
  searchParams.value = {
    platform_order_serial: '',
    serial: '',
    platform_name: '',
    status: '',
  };
  queryParams.value = {};
  getTableData();
};

onMounted(() => {
  pagination.value.sortBy = "id";
  getTableData();
});

const handleDetail = (item) => {
  router.push({ name: "NavigatorDetail", query: { id: item.id } });
};
</script>
