import { useQuasar } from 'quasar'
import { getAction, getActionByPath, postAction, putAction } from 'src/api/manage'
import { computed, reactive, ref } from 'vue'
import { useI18n } from 'vue-i18n'

export default function useRecordDetail(url, emit) {
  const { t } = useI18n()
  const $q = useQuasar()
  const formType = ref('')
  const formTypeName = computed(() => {
    if (formType.value === 'edit') {
      return t('admin.Edit')
    }
    else if (formType.value === 'add') {
      return t('admin.Add')
    }
    else {
      return t('admin.Error')
    }
  })
  const recordDetail = reactive({})
  const recordDetailVisible = ref(false)
  const loading = ref(false)
  const show = (row) => {
    recordDetail.value = {}
    loading.value = true
    recordDetailVisible.value = true
    if (row && row.id) {
      handleQueryById([row.id])
    }
    else {
      recordDetail.value = {}
      recordDetailVisible.value = true
      loading.value = false
    }
  }
  const handleQueryById = async(id) => {
    if (Array.isArray(id)) {
      console.log('id', id)
      console.log('url', url)
      getActionByPath(url.item, id).then(res => {
        if (res.code === 200) {
          recordDetail.value = res.data
        }
      }).finally(() => {
        loading.value = false
      })
    } else {
      getAction(url.item, {
        id,
      }).then(res => {
        if (res.code === 200) {
          recordDetail.value = res.data
        }
      }).finally(() => {
        loading.value = false
      })
    }
  }

  const recordDetailForm = ref(null)

  const handleAddOrEdit = async() => {
    const success = await recordDetailForm.value.validate()
    if (success) {
      if (formType.value === 'edit') {
        if (url === undefined || !url.edit) {
          $q.notify({
            type: 'negative',
            message: '请先配置url',
          })
          return
        }
        const res = await putAction(url.edit, recordDetail.value)
        if (res.code === 200) {
          $q.notify({
            type: 'positive',
            message: res.message,
          })
          recordDetailVisible.value = false
        }
      }
      else if (formType.value === 'add') {
        if (url === undefined || !url.create) {
          $q.notify({
            type: 'negative',
            message: '请先配置url',
          })
          return
        }
        const res = await postAction(url.create, recordDetail.value)
        if (res.code === 200) {
          $q.notify({
            type: 'positive',
            message: res.message,
          })
          recordDetailVisible.value = false
        }
      }
      else {
        $q.notify({
          type: 'negative',
          message: t('admin.CanNotAddOrEdit'),
        })
      }
      emit('handleFinish')
    }
    else {
      $q.notify({
        type: 'negative',
        message: t('admin.FixForm'),
      })
    }
  }
  return {
    loading,
    show,
    formType,
    formTypeName,
    recordDetail,
    recordDetailVisible,
    handleQueryById,
    recordDetailForm,
    handleAddOrEdit,
  }
}
