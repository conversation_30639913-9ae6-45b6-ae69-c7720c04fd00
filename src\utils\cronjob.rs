use crate::{
    app_writer::AppResult,
    entities::{
        cron_job::<PERSON>ron<PERSON><PERSON>,
        cron_job_log::{<PERSON>ronJobLog, CronJobLogBmc},
    },
};
use anyhow::anyhow;
use std::collections::HashMap;
use std::future::Future;
use std::sync::Arc;
use tokio::sync::RwLock;
use tokio_cron_scheduler::{Job, JobScheduler, JobSchedulerGuard};

/// 全局任务调度器
pub struct CronJobScheduler;

impl CronJobScheduler {
    /// 创建并添加一个新的定时任务
    ///
    /// # 类型参数
    /// * `F` - 异步闭包类型
    /// * `Fut` - 异步闭包返回的Future类型
    pub async fn create_job<F, Fut>(&self, cron_job: CronJob, task: F) -> AppResult<String>
    where
        F: FnMut(String, tokio_cron_scheduler::JobSchedulerGuard) -> Fut + Send + Sync + 'static,
        Fut: Future<Output = Result<String, String>> + Send + 'static,
    {
        let mut sched = JobScheduler::new().await?;
        let job_id = cron_job.id.clone().unwrap().to_string();
        let job = Job::new_async(cron_job.cron_expr, move |uuid, l| {
            let job_id = job_id.clone();
            Box::pin(async move {
                // 创建执行记录
                let mut log = CronJobLog::new(job_id, uuid.clone());

                // 执行任务
                match task(uuid.clone(), l).await {
                    Ok(result) => {
                        // 更新执行成功记录
                        log = log.complete(Some(result));
                    }
                    Err(error) => {
                        // 更新执行失败记录
                        log = log.fail(error);
                    }
                }

                // 保存执行记录
                if let Err(e) = CronJobLogBmc::create(log).await {
                    eprintln!("Failed to save job log: {}", e);
                }
            })
        })?;
        sched.add(job.clone()).await?;
        Ok(format!("任务添加成功，任务ID: {}", job.guid()))
    }

    /// 删除指定ID的定时任务
    pub async fn delete_job(&self, job_id: String) -> AppResult<String> {
        let mut jobs = self.jobs.write().await;
        if let Some(job) = jobs.remove(&job_id) {
            self.scheduler
                .write()
                .await
                .remove(&job.guid())
                .await?
                .into();
            Ok("任务删除成功".to_string())
        } else {
            Err(anyhow!("任务不存在").into())
        }
    }

    /// 获取所有任务的ID列表
    pub async fn get_all_job_ids(&self) -> Vec<String> {
        self.jobs.read().await.keys().cloned().collect()
    }

    /// 获取指定ID的任务详情
    pub async fn get_job_details(&self, job_id: &str) -> Option<Job> {
        self.jobs.read().await.get(job_id).cloned()
    }
}
