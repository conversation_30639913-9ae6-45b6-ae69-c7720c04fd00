use std::str::FromStr;

use crate::app_error::AppError;
use crate::app_writer::AppResult;
use crate::db::{Castable, CountRecord, Creatable, Database, Patchable, RelateParams};
use crate::db::{ListOptions, WhereOptions};
use crate::dtos::company::{CompanyCreate, CompanyResponse, CompanyUpdate};
use crate::utils::date::{convert_excel_datetime, get_month_range, get_year_range};
use anyhow::anyhow;
use chrono::Local;
use serde::{Deserialize, Serialize};
use surrealdb::RecordId;

use crate::services::import_record;

#[derive(Debug, Serialize, Deserialize)]
pub struct Company {
    pub id: Option<RecordId>,
    pub creator: Option<String>,
    pub manager: Option<String>,
    pub name: String,
    pub serial: String,
    pub company_type: Option<String>,
    pub registered_address: Option<String>,
    pub corporate: Option<String>,
    pub social_code: Option<String>,
    pub industry_type: Option<String>,
    pub created_at: i64,
    pub updated_at: i64,
}

impl Creatable for Company {}
impl Patchable for Company {}
impl Castable for Company {}

impl Company {
    pub async fn response(self) -> CompanyResponse {
        CompanyResponse {
            id: self.id.unwrap().to_string(),
            name: self.name,
            serial: self.serial,
            creator: self.creator.clone(),
            manager: self.manager.clone(),
            company_type: self.company_type.clone(),
            registered_address: self.registered_address.clone(),
            corporate: self.corporate.clone(),
            social_code: self.social_code.clone(),
            industry_type: self.industry_type.clone(),
            created_at: self.created_at,
            updated_at: self.updated_at,
        }
    }
    pub fn create(company: CompanyCreate) -> Company {
        let time_now = Local::now().timestamp_millis();
        Company {
            id: None,
            creator: company.creator,
            manager: company.manager,
            name: company.name,
            serial: company.serial,
            company_type: company.company_type,
            registered_address: company.registered_address,
            corporate: company.corporate,
            social_code: company.social_code,
            industry_type: company.industry_type,
            created_at: time_now,
            updated_at: time_now,
        }
    }
    pub fn update(new: CompanyUpdate, old: Company) -> Company {
        let creator = if new.creator.is_some() {
            new.creator
        } else {
            old.creator
        };
        let manager = if new.manager.is_some() {
            new.manager
        } else {
            old.manager
        };
        let company_type = if new.company_type.is_some() {
            new.company_type
        } else {
            old.company_type
        };
        let registered_address = if new.registered_address.is_some() {
            new.registered_address
        } else {
            old.registered_address
        };
        let corporate = if new.corporate.is_some() {
            new.corporate
        } else {
            old.corporate
        };
        let social_code = if new.social_code.is_some() {
            new.social_code
        } else {
            old.social_code
        };
        let industry_type = if new.industry_type.is_some() {
            new.industry_type
        } else {
            old.industry_type
        };
        let time_now = Local::now().timestamp_millis();
        Company {
            id: None,
            creator,
            manager,
            name: new.name,
            serial: new.serial,
            company_type,
            registered_address,
            corporate,
            social_code,
            industry_type,
            created_at: old.created_at,
            updated_at: time_now,
        }
    }
}

pub struct CompanyBmc;

impl CompanyBmc {
    const ENTITY: &'static str = "company";

    pub async fn get_total(params: Vec<WhereOptions>) -> AppResult<Option<CountRecord>> {
        Database::exec_query_count(Self::ENTITY, params).await
    }

    pub async fn get_list(
        page: u32,
        limit: u32,
        options: Option<ListOptions>,
        params: Vec<WhereOptions>,
    ) -> AppResult<Vec<Company>> {
        Database::exec_query_list(Self::ENTITY, page, limit, options, params).await
    }

    pub async fn get_by_query(params: Vec<WhereOptions>) -> AppResult<Option<Company>> {
        Database::exec_get_by_query(Self::ENTITY, params).await
    }

    pub async fn get_by_id(id: String) -> AppResult<Option<Company>> {
        Database::exec_get_by_id(Self::ENTITY, &id).await
    }

    pub async fn create(company: CompanyCreate) -> AppResult<String> {
        let obj = Company::create(company);
        Database::exec_create(Self::ENTITY, obj).await
    }

    pub async fn update(company: CompanyUpdate) -> AppResult<String> {
        let check: Option<Company> =
            Database::exec_get_by_id(Self::ENTITY, &company.id.clone()).await?;
        if check.is_none() {
            return Err(AppError::AnyHow(anyhow!("Company not found.")));
        }
        let old = check.unwrap();
        let tid = old.id.clone().unwrap().to_string();
        let final_tid = if tid.contains(':') {
            tid.split(':').nth(1).unwrap_or(&tid).to_string()
        } else {
            tid
        };
        let obj = Company::update(company, old);
        Database::exec_update(Self::ENTITY, &final_tid, obj).await
    }

    pub async fn delete(id: String) -> AppResult<String> {
        Database::exec_delete(Self::ENTITY, id).await
    }

    pub async fn relate_to_item(params: RelateParams, state: &str) -> AppResult<String> {
        let from = vec![params.from];
        Database::exec_relate_batch(state, from, params.to).await
    }
}
