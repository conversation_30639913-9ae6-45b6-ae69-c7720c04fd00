// exec_sum() 函数使用示例
// 展示如何使用新的求和统计功能

use crate::{
    db::{Database, WhereOptions},
    services::sales_order::SalesOrderService,
};

/// 展示 exec_sum 函数的各种使用场景
pub async fn exec_sum_usage_examples() {
    println!("=== exec_sum() 函数使用示例 ===\n");

    // 示例1: 统计所有订单的总金额
    println!("1. 统计所有订单的总金额:");
    let params = vec![];
    match SalesOrderService::sum_field(params, "total_payment").await {
        Ok(result) => println!("   ✓ {}", result),
        Err(e) => println!("   ✗ {}", e),
    }

    // 示例2: 统计已完成订单的总金额
    println!("\n2. 统计已完成订单的总金额:");
    let params = vec![WhereOptions::new("status".to_string(), "completed".to_string())];
    match SalesOrderService::sum_field(params, "amount").await {
        Ok(result) => println!("   ✓ {}", result),
        Err(e) => println!("   ✗ {}", e),
    }

    // 示例3: 统计特定合同下的订单总金额
    println!("\n3. 统计特定合同下的订单总金额:");
    let params = vec![WhereOptions::new("contract_id".to_string(), "contract:123".to_string())];
    match SalesOrderService::sum_field(params, "total_payment").await {
        Ok(result) => println!("   ✓ {}", result),
        Err(e) => println!("   ✗ {}", e),
    }

    // 示例4: 统计特定时间段的订单金额
    println!("\n4. 统计特定时间段的订单金额:");
    let params = vec![
        WhereOptions::new("begin_date".to_string(), "2024-01-01".to_string()),
        WhereOptions::new("end_date".to_string(), "2024-01-31".to_string()),
    ];
    match SalesOrderService::sum_field(params, "amount").await {
        Ok(result) => println!("   ✓ {}", result),
        Err(e) => println!("   ✗ {}", e),
    }

    // 示例5: 统计运费总额
    println!("\n5. 统计运费总额:");
    let params = vec![];
    match SalesOrderService::sum_field(params, "express_fee").await {
        Ok(result) => println!("   ✓ {}", result),
        Err(e) => println!("   ✗ {}", e),
    }

    // 示例6: 统计平台费用总额
    println!("\n6. 统计平台费用总额:");
    let params = vec![WhereOptions::new("platform_name".to_string(), "淘宝".to_string())];
    match SalesOrderService::sum_field(params, "platform_fee_total").await {
        Ok(result) => println!("   ✓ {}", result),
        Err(e) => println!("   ✗ {}", e),
    }
}

/// 展示字段类型验证功能
pub async fn field_type_validation_examples() {
    println!("\n=== 字段类型验证示例 ===\n");

    // 正确的数值字段
    println!("正确的数值字段测试:");
    let valid_fields = vec![
        "amount", "total_payment", "express_fee", "platform_fee_total",
        "sales_price", "cost_price", "quantity", "created_at"
    ];
    
    for field in valid_fields {
        println!("  测试字段: {}", field);
        let params = vec![];
        match Database::exec_sum("sales_order", params, field).await {
            Ok(result) => println!("    ✓ 字段验证通过: {}", field),
            Err(e) => println!("    ✗ 字段验证失败: {}", e),
        }
    }

    // 错误的非数值字段
    println!("\n错误的非数值字段测试:");
    let invalid_fields = vec![
        "status", "customer", "address", "platform_name", "serial"
    ];
    
    for field in invalid_fields {
        println!("  测试字段: {}", field);
        let params = vec![];
        match Database::exec_sum("sales_order", params, field).await {
            Ok(result) => println!("    ⚠ 意外通过: {}", result),
            Err(e) => println!("    ✓ 正确拒绝: 字段 '{}' 不可汇总", field),
        }
    }
}

/// 展示复杂查询条件的使用
pub async fn complex_query_examples() {
    println!("\n=== 复杂查询条件示例 ===\n");

    // 多条件查询：特定状态 + 特定平台
    println!("1. 多条件查询 - 特定状态 + 特定平台:");
    let params = vec![
        WhereOptions::new("status".to_string(), "completed".to_string()),
        WhereOptions::new("platform_name".to_string(), "淘宝".to_string()),
    ];
    match SalesOrderService::sum_field(params, "total_payment").await {
        Ok(result) => println!("   ✓ {}", result),
        Err(e) => println!("   ✗ {}", e),
    }

    // 时间范围查询
    println!("\n2. 时间范围查询:");
    let params = vec![
        WhereOptions::new("begin_date".to_string(), "2024-01-01".to_string()),
        WhereOptions::new("end_date".to_string(), "2024-12-31".to_string()),
    ];
    match SalesOrderService::sum_field(params, "amount").await {
        Ok(result) => println!("   ✓ {}", result),
        Err(e) => println!("   ✗ {}", e),
    }

    // ID列表查询
    println!("\n3. ID列表查询:");
    let params = vec![
        WhereOptions::new("ids".to_string(), "sales_order:1,sales_order:2,sales_order:3".to_string()),
    ];
    match SalesOrderService::sum_field(params, "total_payment").await {
        Ok(result) => println!("   ✓ {}", result),
        Err(e) => println!("   ✗ {}", e),
    }
}

/// 展示不同表的求和操作
pub async fn different_tables_examples() {
    println!("\n=== 不同表的求和操作示例 ===\n");

    // 销售订单信息表求和
    println!("1. 销售订单信息表求和:");
    let params = vec![WhereOptions::new("order_serial".to_string(), "SO001".to_string())];
    match Database::exec_sum("sales_order_info", params, "total_sales_price").await {
        Ok(result) => println!("   ✓ {}", result),
        Err(e) => println!("   ✗ {}", e),
    }

    // 采购订单表求和
    println!("\n2. 采购订单表求和:");
    let params = vec![WhereOptions::new("status".to_string(), "completed".to_string())];
    match Database::exec_sum("purchase_order", params, "total_payment").await {
        Ok(result) => println!("   ✓ {}", result),
        Err(e) => println!("   ✗ {}", e),
    }
}

/// 展示SQL生成过程
pub fn sql_generation_examples() {
    println!("\n=== SQL生成示例 ===\n");

    println!("1. 无条件求和:");
    println!("   输入: table='sales_order', field='total_payment', params=[]");
    println!("   生成SQL: SELECT math::sum(total_payment) AS sum_result FROM sales_order;");

    println!("\n2. 单条件求和:");
    println!("   输入: table='sales_order', field='amount', params=[status='completed']");
    println!("   生成SQL: SELECT math::sum(amount) AS sum_result FROM sales_order WHERE status = 'completed';");

    println!("\n3. 多条件求和:");
    println!("   输入: table='sales_order', field='total_payment'");
    println!("   条件: [status='completed', platform_name='淘宝']");
    println!("   生成SQL: SELECT math::sum(total_payment) AS sum_result FROM sales_order WHERE status = 'completed' AND platform_name = '淘宝';");

    println!("\n4. 时间范围求和:");
    println!("   输入: table='sales_order', field='amount'");
    println!("   条件: [begin_date='2024-01-01', end_date='2024-01-31']");
    println!("   生成SQL: SELECT math::sum(amount) AS sum_result FROM sales_order WHERE date >= '2024-01-01' AND date <= '2024-01-31';");
}

/// 展示错误处理场景
pub async fn error_handling_examples() {
    println!("\n=== 错误处理示例 ===\n");

    // 不可汇总字段错误
    println!("1. 不可汇总字段错误:");
    let params = vec![];
    match Database::exec_sum("sales_order", params, "customer").await {
        Ok(result) => println!("   ⚠ 意外成功: {}", result),
        Err(e) => println!("   ✓ 正确处理错误: {}", e),
    }

    // 不存在的字段错误
    println!("\n2. 不存在的字段错误:");
    let params = vec![];
    match Database::exec_sum("sales_order", params, "non_existent_field").await {
        Ok(result) => println!("   ⚠ 意外成功: {}", result),
        Err(e) => println!("   ✓ 正确处理错误: {}", e),
    }

    // 空结果处理
    println!("\n3. 空结果处理:");
    let params = vec![WhereOptions::new("status".to_string(), "non_existent_status".to_string())];
    match Database::exec_sum("sales_order", params, "total_payment").await {
        Ok(result) => println!("   ✓ 正确处理空结果: {}", result),
        Err(e) => println!("   ✗ 处理失败: {}", e),
    }
}

/// 主函数示例
pub async fn run_all_sum_examples() {
    println!("🧮 exec_sum() 函数完整使用示例\n");
    
    // 运行各种示例
    exec_sum_usage_examples().await;
    field_type_validation_examples().await;
    complex_query_examples().await;
    different_tables_examples().await;
    sql_generation_examples();
    error_handling_examples().await;
    
    println!("\n✅ 所有求和示例运行完成！");
}
