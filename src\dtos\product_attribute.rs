use crate::db::{Castable, Creatable, Patchable};
use crate::utils::date::{default_now, default_zero};
use salvo::prelude::{Extractible, ToSchema};
use serde::{Deserialize, Serialize};
use validator::Validate;

#[derive(Default, Clone, Deserialize, Serialize, Debug, Validate, ToSchema)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct ProductAttributeCreate {
    pub product_serial: String,
    pub stable: bool,
    pub status: String,
    pub name: String,
    pub value: String,
    #[serde(default = "default_zero")]
    pub sort: i64,
    #[serde(default = "default_now")]
    pub created_at: i64,
    #[serde(default = "default_now")]
    pub updated_at: i64,
}

impl Creatable for ProductAttributeCreate {}

#[derive(Default, Clone, Deserialize, Serialize, Debug, Extractible, ToSchema)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct ProductAttributeUpdate {
    pub id: String,
    pub product_serial: String,
    pub stable: bool,
    pub status: String,
    pub name: String,
    pub value: String,
    #[serde(default = "default_zero")]
    pub sort: i64,
    #[serde(default = "default_now")]
    pub created_at: i64,
    #[serde(default = "default_now")]
    pub updated_at: i64,
}

impl Patchable for ProductAttributeUpdate {}

#[derive(Deserialize, Serialize, Debug, Clone, Default, ToSchema)]
pub struct ProductAttributeResponse {
    pub id: String,
    pub product_serial: String,
    pub stable: bool,
    pub status: String,
    pub name: String,
    pub value: String,
    pub sort: i64,
    pub created_at: i64,
    pub updated_at: i64,
}

impl Castable for ProductAttributeResponse {}
