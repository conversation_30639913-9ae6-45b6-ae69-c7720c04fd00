<template>
  <q-dialog v-model="recordDetailVisible" persistent full-height position="right">
    <q-card style="width: 800px; max-width: 80vw; height: 100%">
      <q-card-section>
        <div class="text-h6">
          {{ formTypeName }} {{ t("admin.Category") }} :

          {{ recordDetail.value.name }}
        </div>
      </q-card-section>

      <q-separator />

      <q-card-section>
        <q-form @submit="handleAddOrEdit" ref="recordDetailForm">
          <div class="row">
            <q-input v-model="recordDetail.value.name" class="col q-mx-sm" :label="t('admin.Name')"
              :rules="[(val) => !!val || t('admin.Required')]" />
            <q-select v-model="recordDetail.value.category_type" class="col q-mx-sm" :options="categoryTypeOptions"
              :label="t('admin.CategoryType')" emit-value map-options
              :rules="[(val) => !!val || t('admin.Required')]" />
          </div>
          <div class="row">
            <q-select v-model="recordDetail.value.parent" class="col q-mx-sm" :options="parentOptions"
              :label="t('admin.Parent')" emit-value map-options />
            <q-input v-model.number="recordDetail.value.order" class="col q-mx-sm" type="number"
              :label="t('admin.Order')" :rules="[(val) => !!val || t('admin.Required')]" />
          </div>
          <div class="row">
            <q-field dense class="col q-mx-sm" :label="$t('admin.Status')" stack-label>
              <template #control>
                <q-option-group v-model="recordDetail.value.status" :options="statusOptions" color="primary" inline />
              </template>
            </q-field>
          </div>
          <q-input v-model="recordDetail.value.description" type="textarea" :label="t('admin.Description')"
            class="q-mx-sm" />
        </q-form>
      </q-card-section>

      <q-separator />

      <q-card-actions align="right">
        <q-btn :label="t('admin.Save')" color="primary" @click="handleAddOrEdit" :loading="loading" />
        <q-btn :label="t('admin.Cancel')" color="negative" v-close-popup />
      </q-card-actions>

      <q-inner-loading :showing="loading">
        <q-spinner-gears size="50px" color="primary" />
      </q-inner-loading>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { useQuasar } from "quasar";
import { api } from "src/boot/axios";
import useRecordDetail from "src/composables/useRecordDetail";
import { computed, ref } from "vue";
import { useI18n } from "vue-i18n";

const props = defineProps({
  editMode: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(["handleFinish"]);
const { t } = useI18n();
const $q = useQuasar();

// 定义API URL
const url = {
  item: "/api/category",
  create: "/api/category",
  edit: "/api/category",
};

// 使用 useRecordDetail 组合式函数
const {
  formType,
  formTypeName,
  recordDetail,
  recordDetailVisible,
  loading,
  recordDetailForm,
  handleQueryById,
  handleAddOrEdit
} = useRecordDetail(url, emit);

const isParent = ref(false);
const parentName = ref("");

const statusOptions = [
  { label: "启用", value: "on" },
  { label: "停用", value: "off" },
];

const parentOptions = [{ label: "无", value: "" }];

const categoryTypeOptions = [
  { label: "文章", value: "article" },
  { label: "产品", value: "product" },
];

const dialogTitle = computed(() => {
  return props.editMode
    ? t("admin.Edit") + t("admin.Category")
    : t("admin.Add") + t("admin.Category");
});

const show = (row) => {
  recordDetail.value = {};
  loading.value = true;
  recordDetailVisible.value = true;
  if (row?.id) {
    handleQueryById([row.id]);
  } else {
    recordDetail.value = {
      name: "",
      description: "",
      parent: parentName.value,
      category_type: "",
      order: 0,
      status: "on",
    };
    loading.value = false;
  }
};

defineExpose({
  show,
  formType,
  isParent,
  parentName,
  recordDetail,
});
</script>
