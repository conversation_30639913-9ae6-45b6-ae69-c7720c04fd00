<template>
  <base-content scrollable>
    <q-card class="q-ma-md">
      <q-card-section class="row">
        <div class="text-h6 q-mx-sm col-auto">
          管理还款计划
          <q-badge v-if="itemDetail.status" :color="getStatusColor(itemDetail.status)" :label="getStatusLabel(itemDetail.status)" class="q-ml-sm" />
        </div>
        <div class="col">
          <q-btn label="更新计划" color="primary" class="q-mx-sm" @click="handleSaveAction" />
          <q-btn v-close-popup label="关闭页面" color="negative" class="q-mx-sm" @click="closeTab" />
        </div>
      </q-card-section>

      <q-separator />

      <q-card-section>
        <q-form ref="recordDetailForm">
          <!-- 基本信息 -->
          <div class="text-subtitle1 q-mb-md text-primary">基本信息</div>
          <div class="row q-gutter-md q-mb-md">
            <div class="col">
              <q-input v-model="itemDetail.serial" label="还款计划编号" outlined dense hint="如果不手动填写计划编号，系统将自动生成" />
            </div>
            <div class="col">
              <q-input v-model="itemDetail.begin_date" label="计息开始日期" outlined dense type="date" />
            </div>
            <div class="col">
              <q-input v-model="itemDetail.end_date" label="预计还款日期" outlined dense type="date" />
            </div>
          </div>

          <!-- 金额信息 -->
          <div class="text-subtitle1 q-mb-md text-primary">金额信息</div>
          <div class="row q-gutter-md q-mb-md">
            <div class="col">
              <q-input v-model.number="itemDetail.principal_predict" label="支取本金金额" outlined dense type="number" step="0.01" />
            </div>
            <div class="col">
              <q-input v-model.number="itemDetail.target_amount" label="已选订单费用总额" outlined dense disable="true" />
            </div>
          </div>

          <!-- 利润计算配置 -->
          <div class="text-subtitle1 q-mb-md text-primary">利润计算配置</div>
          <div class="row q-gutter-md q-mb-md">
            <div class="col">
              <q-input v-model.number="itemDetail.profit_calc_fee" label="利润计算费率" outlined dense type="number" step="0.0001" hint="年化利率，如0.12表示12%" />
            </div>
            <div class="col">
              <q-select v-model="itemDetail.profit_calc_period" :options="calcPeriodDict" option-label="label" option-value="value"
                emit-value map-options label="利润计算周期" outlined dense />
            </div>
            <div class="col">
              <q-input v-model.number="itemDetail.penalty_calc_fee" label="违约金计算费率" outlined dense type="number" step="0.0001" hint="日息利率，如0.0005表示0.05%" />
            </div>
          </div>

          <!-- 备注信息 -->
          <div class="text-subtitle1 q-mb-md text-primary">备注信息</div>
          <div class="row ">
            <div class="col-12">
              <q-input v-model="itemDetail.remark" label="还款计划备注" outlined dense type="textarea" rows="3" />
            </div>
          </div>
        </q-form>
      </q-card-section>
    </q-card>

    <q-card class="q-ma-md">

      <q-card-section>
        <q-btn label="关联订单" color="primary" @click="getOrderList" />
      </q-card-section>
    </q-card>
    <SelectOrderList v-if="route.query.id" ref="selectOrderListDialogue" :url-list="url.order"
      :contract-id="route.query.id" @handleSelectItem="handleSelectOrder" />
  </base-content>
</template>

<script setup>
import { Notify } from "quasar";
import { postAction, getActionByPath } from "src/api/manage";
import BaseContent from "src/components/BaseContent/BaseContent.vue";
import { useTagViewStore } from "src/stores/tagView";
import { onMounted, ref } from "vue";
import { useRoute } from "vue-router";
import SelectOrderList from "src/components/SelectOrderList/SelectItemDialog.vue";

const tabMenuStore = useTagViewStore();
const route = useRoute();
const url = {
  item: "/api/repayment",
  create: "/api/repayment",
  edit: "/api/repayment",
  order: "/api/sales_order/list",
};

const itemDetail = ref({});

const recordDetailForm = ref();

const selectOrderListDialogue = ref(null);

const getOrderList = () => {
  selectOrderListDialogue.value.show();
};

const handleSelectOrder = async (items) => {
  for (const item of items) {
    console.log(item);
  }
}

onMounted(async () => {
  console.log(route.query.id, "当前route");
  if (route.query.id) {
    await handleGetDetail();
  } else {
    closeTab();
  }

});

// 项目状态字典
const statusDict = [
  { label: "草稿", value: "draft" },
  { label: "待审核", value: "new" },
  { label: "已审核", value: "processing" },
  { label: "待还款", value: "pending" },
  { label: "部分还款", value: "partial" },
  { label: "已完成", value: "completed" },
  { label: "逾期", value: "overdue" },
];

// 获取状态标签
const getStatusLabel = (status) => {
  const statusItem = statusDict.find(item => item.value === status);
  return statusItem ? statusItem.label : status;
};

// 获取状态颜色
const getStatusColor = (status) => {
  const colorMap = {
    'draft': 'grey',
    'new': 'blue',
    'processing': 'orange',
    'pending': 'purple',
    'partial': 'amber',
    'completed': 'green',
    'overdue': 'red'
  };
  return colorMap[status] || 'grey';
};

// 计算周期字典
const calcPeriodDict = [
  { label: "单次", value: "ONCE" },
  { label: "按天", value: "DAY" },
  { label: "按月", value: "MONTH" },
  { label: "按年", value: "YEAR" },
  { label: "按季度", value: "QUARTER" },
];

const handleGetDetail = async () => {
  const res = await getActionByPath(url.item, [route.query.id]);
  if (res.code === 200) {
    itemDetail.value = res.data;
  }
};

const handleSaveAction = async () => {
  const success = await recordDetailForm.value.validate();
  if (success) {
    if (url === undefined || !url.item) {
      Notify.create({
        type: "negative",
        message: "请先配置url",
      });
      return;
    }
    const res = await postAction(url.item, itemDetail.value);
    if (res.code === 200) {
      Notify.create({
        type: "positive",
        message: res.msg,
      });
      closeTab();
    }
  } else {
    Notify.create({
      type: "negative",
      message: "请检查表单信息是否正确",
    });
  }
};

const closeTab = () => {
  tabMenuStore.removeTagViewByFullPath(route.fullPath);
};
</script>

<style scoped lang="scss">
.product-detail {
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;

  .title-place {
    font-weight: 600;
    color: rgb(58, 111, 204);
  }

  .product-detail-row {
    padding: 5px;
  }

  .product-detail-label:after {
    content: "=";
    display: inline-block;
    padding: 0 12px;
    color: #26ceba;
  }

  .product-detail-value:before {
    content: "( ";
    color: #ffc069;
  }

  .product-detail-value:after {
    content: " )";
    color: #ffc069;
  }
}

.attr-label:after {
  content: "=";
  display: inline-block;
  padding: 0 12px;
  color: #26ceba;
}
</style>
