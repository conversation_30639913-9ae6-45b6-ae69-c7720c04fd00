use crate::{
    app_writer::AppResult,
    db::{ListParams, WhereOptions},
    dtos::quota_contract::{QuotaContractCreate, QuotaContractUpdate},
    entities::quota_contract::{QuotaContract, QuotaContractBmc},
};
use anyhow::anyhow;

use super::financial_contract;

pub struct QuotaContractService;
impl QuotaContractService {
    pub async fn get_list(req: ListParams) -> AppResult<Vec<QuotaContract>> {
        let mut limit: u32 = 15;
        let mut offset: u32 = 0;
        if let Some(page) = req.page {
            limit = page.get_limit().unwrap_or(0);
            offset = page.get_offset();
        }
        let mut where_options = Vec::new();
        if let Some(params) = req.params {
            where_options = params;
        }
        let res = QuotaContractBmc::get_list(offset, limit, req.options, where_options).await?;
        Ok(res)
    }

    pub async fn get_by_query(params: Vec<WhereOptions>) -> AppResult<QuotaContract> {
        match QuotaContractBmc::get_by_query(params).await {
            Ok(res) => {
                if let Some(res) = res {
                    Ok(res)
                } else {
                    Err(anyhow!("QuotaContract not found.").into())
                }
            }
            Err(e) => Err(e),
        }
    }

    pub async fn get_by_id(id: String) -> AppResult<QuotaContract> {
        match QuotaContractBmc::get_by_id(&id).await {
            Ok(res) => {
                if let Some(res) = res {
                    Ok(res)
                } else {
                    Err(anyhow!("QuotaContract not found.").into())
                }
            }
            Err(e) => Err(e),
        }
    }

    pub async fn create(req: QuotaContractCreate) -> AppResult<String> {
        QuotaContractBmc::create(req).await?;
        Ok("QuotaContract created".to_string())
    }

    pub async fn update(req: QuotaContractUpdate) -> AppResult<String> {
        QuotaContractBmc::update(req).await?;
        Ok("QuotaContract updated".to_string())
    }

    pub async fn delete(id: String) -> AppResult<String> {
        QuotaContractBmc::delete(id).await?;
        Ok("QuotaContract deleted".to_string())
    }
}
