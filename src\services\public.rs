use anyhow::anyhow;

use crate::{
    app_writer::AppR<PERSON><PERSON>,
    dtos::user::{UserLoginRequest, UserLoginResponse},
    entities::user::UserBmc,
    middleware::jwt::get_token,
};

pub struct PublicService;
impl PublicService {
    pub async fn login(req: UserLoginRequest) -> AppResult<UserLoginResponse> {
        let user = UserBmc::auth_user(req.clone().username, req.clone().password).await?;
        if user.is_none() {
            return Err(anyhow!("用户不存在。").into());
        }
        let user = user.unwrap().response();
        let (token, exp) = get_token(user.clone())?;
        let res = UserLoginResponse { token, exp };
        Ok(res)
    }
}
