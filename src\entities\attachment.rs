use std::str::FromStr;

use crate::{
    app_writer::AppResult,
    db::{
        Castable, CountRecord, Creatable, Database, ListOptions, Patchable, RelateParams,
        WhereOptions,
    },
    dtos::attachment::{AttachmentCreate, AttachmentResponse, AttachmentUpdate},
};
use chrono::Local;
use serde::{Deserialize, Serialize};
use surrealdb::RecordId;

#[derive(Debug, Serialize, Deserialize)]
pub struct Attachment {
    pub id: Option<RecordId>,
    pub title: Option<String>,
    pub entity_type: String,
    pub file_type: Option<String>,
    pub save_dir: String,
    pub file_name: String,
    pub file_link: String,
    pub thumb_name: Option<String>,
    pub thumb_link: Option<String>,
    pub status: String,
    pub sort: i64,
    pub created_at: i64,
    pub updated_at: i64,
}

impl Creatable for Attachment {}
impl Patchable for Attachment {}
impl Castable for Attachment {}

impl Attachment {
    pub fn response(self) -> AttachmentResponse {
        AttachmentResponse {
            id: self.id.unwrap().to_string(),
            title: self.title,
            entity_type: self.entity_type,
            file_type: self.file_type,
            save_dir: self.save_dir,
            file_name: self.file_name,
            file_link: self.file_link,
            thumb_name: self.thumb_name,
            thumb_link: self.thumb_link,
            status: self.status,
            sort: self.sort,
            created_at: self.created_at,
            updated_at: self.updated_at,
        }
    }
    pub fn create(obj: AttachmentCreate) -> Attachment {
        let time_now = Local::now().timestamp_millis();
        Attachment {
            id: None,
            title: obj.title,
            entity_type: obj.entity_type,
            file_type: obj.file_type,
            save_dir: obj.save_dir,
            file_name: obj.file_name,
            file_link: obj.file_link,
            thumb_name: obj.thumb_name,
            thumb_link: obj.thumb_link,
            status: obj.status,
            sort: obj.sort,
            created_at: time_now,
            updated_at: time_now,
        }
    }
}

pub struct AttachmentBmc;

impl AttachmentBmc {
    const ENTITY: &'static str = "attachment";

    pub async fn get_list(
        page: u32,
        limit: u32,
        options: Option<ListOptions>,
        params: Vec<WhereOptions>,
    ) -> AppResult<Vec<Attachment>> {
        Database::exec_query_list(Self::ENTITY, page, limit, options, params).await
    }

    pub async fn get_total(params: Vec<WhereOptions>) -> AppResult<Option<CountRecord>> {
        Database::exec_query_count(Self::ENTITY, params).await
    }

    pub async fn get_by_query(params: Vec<WhereOptions>) -> AppResult<Option<Attachment>> {
        Database::exec_get_by_query(Self::ENTITY, params).await
    }

    pub async fn get_by_id(id: &str) -> AppResult<Option<Attachment>> {
        Database::exec_get_by_id(Self::ENTITY, id).await
    }

    pub async fn create(attachment: AttachmentCreate) -> AppResult<String> {
        let obj = Attachment::create(attachment);
        Database::exec_create(Self::ENTITY, obj).await
    }

    pub async fn update(attachment: AttachmentUpdate) -> AppResult<String> {
        let tid = attachment.id.clone();
        Database::exec_update(Self::ENTITY, &tid.to_string(), attachment).await
    }

    pub async fn delete(id: String) -> AppResult<String> {
        Database::exec_delete(Self::ENTITY, id).await
    }

    pub async fn relate_to_item(params: RelateParams, state: &str) -> AppResult<String> {
        let from = vec![params.from];
        Database::exec_relate_batch(state, from, params.to).await
    }
}
