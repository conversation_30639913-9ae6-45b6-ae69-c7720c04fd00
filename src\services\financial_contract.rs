use crate::{
    app_writer::AppR<PERSON>ult,
    db::{CreateParams, ListParams, UpdateParams, WhereOptions},
    dtos::financial_contract::{FinancialContractCreate, FinancialContractUpdate},
    entities::financial_contract::{FinancialContract, FinancialContractBmc},
};
use anyhow::anyhow;

pub struct FinancialContractService;
impl FinancialContractService {
    pub async fn get_total(req: Option<Vec<WhereOptions>>) -> AppResult<u32> {
        let mut where_options = Vec::new();
        if let Some(params) = req {
            where_options = params;
        }
        match FinancialContractBmc::get_total(where_options).await {
            Ok(count) => {
                if let Some(count) = count {
                    Ok(count.count)
                } else {
                    Ok(0)
                }
            }
            Err(_) => Ok(0),
        }
    }

    pub async fn get_list(req: ListParams) -> AppResult<Vec<FinancialContract>> {
        let mut limit: u32 = 15;
        let mut offset: u32 = 0;
        if let Some(page) = req.page {
            limit = page.get_limit().unwrap_or(0);
            offset = page.get_offset();
        }
        let mut where_options = Vec::new();
        if let Some(params) = req.params {
            where_options = params;
        }
        let res = FinancialContractBmc::get_list(offset, limit, req.options, where_options).await?;
        Ok(res)
    }

    pub async fn get_by_query(params: Vec<WhereOptions>) -> AppResult<FinancialContract> {
        match FinancialContractBmc::get_by_query(params).await {
            Ok(group) => {
                if let Some(group) = group {
                    Ok(group)
                } else {
                    Err(anyhow!("FinancialContract not found").into())
                }
            }
            Err(e) => Err(e),
        }
    }

    pub async fn get_by_id(id: String) -> AppResult<FinancialContract> {
        match FinancialContractBmc::get_by_id(&id).await {
            Ok(group) => {
                if let Some(group) = group {
                    Ok(group)
                } else {
                    Err(anyhow!("FinancialContract not found").into())
                }
            }
            Err(e) => Err(e),
        }
    }

    pub async fn create(req: CreateParams<FinancialContractCreate>) -> AppResult<String> {
        let res = FinancialContractBmc::create(req.data).await?;
        Ok(res)
    }

    pub async fn update(req: UpdateParams<FinancialContractUpdate>) -> AppResult<String> {
        FinancialContractBmc::update(req.data).await?;
        Ok("FinancialContract updated".to_string())
    }

    pub async fn delete(id: String) -> AppResult<String> {
        FinancialContractBmc::delete(id).await?;
        Ok("FinancialContract deleted".to_string())
    }
}
