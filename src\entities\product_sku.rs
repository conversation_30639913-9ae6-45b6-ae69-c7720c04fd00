use std::str::FromStr;

use crate::{
    app_writer::AppResult,
    db::{
        Castable, CountRecord, Creatable, Database, ListOptions, Patchable, RelateParams,
        WhereOptions,
    },
    dtos::product_sku::{ProductSkuCreate, ProductSkuResponse, ProductSkuUpdate},
};
use chrono::Local;
use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};
use surrealdb::RecordId;

#[derive(Debug, Serialize, Deserialize)]
pub struct ProductSku {
    pub id: Option<RecordId>,
    pub sort: i64,
    pub status: String,
    pub serial: String,
    pub product_serial: Option<String>,
    pub external_serial: Option<String>,
    pub product_type: Option<String>,
    pub sales_price: Decimal,
    pub market_price: Decimal,
    pub cost_price: Decimal,
    pub name: Option<String>,
    pub brand: Option<String>,
    pub barcode: Option<String>,
    pub model: Option<String>,
    pub specification: Option<String>,
    pub size: Option<String>,
    pub package: Option<String>,
    pub unit: Option<String>,
    pub source_area: Option<String>,
    pub desc: Option<String>,
    pub content: Option<String>,
    pub supplier_id: Option<RecordId>,
    created_at: Option<i64>,
    updated_at: Option<i64>,
}

impl Creatable for ProductSku {}
impl Patchable for ProductSku {}
impl Castable for ProductSku {}

impl ProductSku {
    pub fn response(self) -> ProductSkuResponse {
        let supplier_id = if self.supplier_id.is_some() {
            Some(self.supplier_id.unwrap().to_string())
        } else {
            None
        };
        ProductSkuResponse {
            id: self.id.unwrap().to_string(),
            sort: self.sort,
            status: self.status,
            serial: self.serial,
            product_serial: self.product_serial,
            external_serial: self.external_serial,
            product_type: self.product_type,
            sales_price: self.sales_price,
            market_price: self.market_price,
            cost_price: self.cost_price,
            name: self.name,
            brand: self.brand,
            barcode: self.barcode,
            model: self.model,
            specification: self.specification,
            size: self.size,
            package: self.package,
            unit: self.unit,
            source_area: self.source_area,
            desc: self.desc,
            content: self.content,
            supplier_id,
            created_at: self.created_at,
            updated_at: self.updated_at,
            image: None,
            attachment: None,
        }
    }
    pub fn draft(obj: ProductSkuCreate) -> ProductSku {
        let supplier_id = if obj.supplier_id.is_some() {
            Some(RecordId::from_str(&obj.supplier_id.unwrap()).unwrap())
        } else {
            None
        };
        let time_now = Some(Local::now().timestamp_millis());
        ProductSku {
            id: None,
            sort: obj.sort,
            status: "draft".to_string(),
            serial: obj.serial,
            product_serial: obj.product_serial,
            external_serial: obj.external_serial,
            product_type: obj.product_type,
            sales_price: obj.sales_price,
            market_price: obj.market_price,
            cost_price: obj.cost_price,
            name: obj.name,
            brand: obj.brand,
            barcode: obj.barcode,
            model: obj.model,
            specification: obj.specification,
            size: obj.size,
            package: obj.package,
            unit: obj.unit,
            source_area: obj.source_area,
            desc: obj.desc,
            content: obj.content,
            supplier_id,
            created_at: time_now,
            updated_at: time_now,
        }
    }
    pub fn create(obj: ProductSkuCreate) -> ProductSku {
        let supplier_id = if obj.supplier_id.is_some() {
            Some(RecordId::from_str(&obj.supplier_id.unwrap()).unwrap())
        } else {
            None
        };
        let time_now = Some(Local::now().timestamp_millis());
        ProductSku {
            id: None,
            sort: obj.sort,
            status: obj.status,
            serial: obj.serial,
            product_serial: obj.product_serial,
            external_serial: obj.external_serial,
            product_type: obj.product_type,
            sales_price: obj.sales_price,
            market_price: obj.market_price,
            cost_price: obj.cost_price,
            name: obj.name,
            brand: obj.brand,
            barcode: obj.barcode,
            model: obj.model,
            specification: obj.specification,
            size: obj.size,
            package: obj.package,
            unit: obj.unit,
            source_area: obj.source_area,
            desc: obj.desc,
            content: obj.content,
            supplier_id,
            created_at: time_now,
            updated_at: time_now,
        }
    }
    pub fn update(new: ProductSkuUpdate, old: ProductSku) -> ProductSku {
        let supplier_id = if new.supplier_id.is_some() {
            Some(RecordId::from_str(&new.supplier_id.unwrap()).unwrap())
        } else {
            old.supplier_id
        };
        let time_now = Some(Local::now().timestamp_millis());
        ProductSku {
            id: None,
            sort: new.sort,
            status: new.status,
            serial: new.serial,
            product_serial: new.product_serial.or(old.product_serial),
            external_serial: new.external_serial.or(old.external_serial),
            product_type: new.product_type.or(old.product_type),
            sales_price: new.sales_price,
            market_price: new.market_price,
            cost_price: new.cost_price,
            name: new.name.or(old.name),
            brand: new.brand.or(old.brand),
            barcode: new.barcode.or(old.barcode),
            model: new.model.or(old.model),
            specification: new.specification.or(old.specification),
            size: new.size.or(old.size),
            package: new.package.or(old.package),
            unit: new.unit.or(old.unit),
            source_area: new.source_area.or(old.source_area),
            desc: new.desc.or(old.desc),
            content: new.content.or(old.content),
            supplier_id,
            created_at: old.created_at,
            updated_at: time_now,
        }
    }
}

pub struct ProductSkuBmc;

#[allow(dead_code)]
impl ProductSkuBmc {
    const ENTITY: &'static str = "product_sku";

    pub async fn get_list(
        page: u32,
        limit: u32,
        options: Option<ListOptions>,
        params: Vec<WhereOptions>,
    ) -> AppResult<Vec<ProductSku>> {
        Database::exec_query_list(Self::ENTITY, page, limit, options, params).await
    }

    pub async fn get_total(params: Vec<WhereOptions>) -> AppResult<Option<CountRecord>> {
        Database::exec_query_count(Self::ENTITY, params).await
    }

    pub async fn get_by_query(params: Vec<WhereOptions>) -> AppResult<Option<ProductSku>> {
        Database::exec_get_by_query(Self::ENTITY, params).await
    }

    pub async fn get_by_id(id: &str) -> AppResult<Option<ProductSku>> {
        Database::exec_get_by_id(Self::ENTITY, id).await
    }

    pub async fn draft(product_sku: ProductSkuCreate) -> AppResult<String> {
        let obj = ProductSku::draft(product_sku);
        Database::exec_create(Self::ENTITY, obj).await
    }

    pub async fn create(product_sku: ProductSkuCreate) -> AppResult<String> {
        let obj = ProductSku::create(product_sku);
        Database::exec_create(Self::ENTITY, obj).await
    }

    pub async fn update(product_sku: ProductSkuUpdate) -> AppResult<String> {
        let tid = product_sku.id.clone();
        let old = match ProductSkuBmc::get_by_id(&tid.to_string()).await {
            Ok(res) => {
                if let Some(res) = res {
                    res
                } else {
                    return Err(anyhow::anyhow!("ProductSku not found.").into());
                }
            }
            Err(e) => return Err(e),
        };
        let tid = old.id.clone().unwrap().to_string();
        let final_tid = if tid.contains(':') {
            tid.split(':').nth(1).unwrap_or(&tid).to_string()
        } else {
            tid
        };
        let obj = ProductSku::update(product_sku, old);
        Database::exec_update(Self::ENTITY, &final_tid, obj).await
    }

    pub async fn delete(id: String) -> AppResult<String> {
        Database::exec_delete(Self::ENTITY, id).await
    }

    pub async fn relate_to_item(params: RelateParams, state: &str) -> AppResult<String> {
        let from = vec![params.from];
        Database::exec_relate_batch(state, from, params.to).await
    }
}
