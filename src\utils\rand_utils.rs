use anyhow::Context;
use argon2::{password_hash::SaltString, Argon2, PasswordHash};
use rand::seq::IteratorRandom;
use rand::Rng;
use std::iter;
use std::time::{SystemTime, UNIX_EPOCH};
use uuid::Uuid;
///  生成指定长度的字符串
#[allow(dead_code)]
#[inline]
pub fn random_string(limit: usize) -> String {
    iter::repeat(())
        .map(|_| rand::thread_rng().sample(rand::distributions::Alphanumeric))
        .map(char::from)
        .take(limit)
        .collect()
}

/// 生成指定长度的大写字母和数字混合的字符串，包含时间戳和UUID以确保唯一性
/// 返回的字符串格式为: 字母开头 + 随机字母数字 + 时间戳后6位 + UUID前4位
pub fn random_uppercase_serial(prefix: Option<String>, limit: usize) -> String {
    if limit == 0 {
        return String::new();
    }

    // 获取当前时间戳（纳秒级）
    let timestamp = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap_or_default()
        .as_nanos();
    let time_part = format!("{:06}", timestamp % 1_000_000);

    // 生成UUID并取前4位
    let uuid_part = Uuid::new_v4().to_string()[..4].to_string();

    let mut rng = rand::thread_rng();
    let first_char = (b'A'..=b'Z').map(char::from).choose(&mut rng).unwrap();

    // 计算随机部分的长度，确保总长度符合limit要求
    let random_part_length = limit.saturating_sub(11).max(0); // 11 = 1(首字母) + 6(时间戳) + 4(UUID)

    let rest: String = iter::repeat(())
        .map(|_| {
            let digit = rng.gen_bool(0.5);
            if digit {
                rng.gen_range(b'0'..=b'9') as char
            } else {
                (b'A'..=b'Z').map(char::from).choose(&mut rng).unwrap()
            }
        })
        .take(random_part_length)
        .collect();

    let result = format!("{}{}{}{}", first_char, rest, time_part, uuid_part);
    if let Some(prefix) = prefix {
        format!("{}-{}", prefix, result)
    } else {
        result
    }
}

pub async fn verify_password(password: String, password_hash: String) -> anyhow::Result<()> {
    tokio::task::spawn_blocking(move || -> anyhow::Result<()> {
        let hash = PasswordHash::new(&password_hash)
            .map_err(|e| anyhow::anyhow!("invalid password hash: {}", e))?;
        let result = hash.verify_password(&[&Argon2::default()], password);
        match result {
            Ok(_) => Ok(()),
            Err(_) => Err(anyhow::anyhow!("invalid password")),
        }
    })
    .await
    .context("panic in verifying password hash")?
}

pub async fn hash_password(password: String) -> anyhow::Result<String> {
    tokio::task::spawn_blocking(move || -> anyhow::Result<String> {
        let salt = SaltString::generate(rand::thread_rng());
        Ok(PasswordHash::generate(Argon2::default(), password, &salt)
            .map_err(|e| anyhow::anyhow!("failed to generate password hash: {}", e))?
            .to_string())
    })
    .await
    .context("panic in generating password hash")?
}
