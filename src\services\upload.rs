use crate::db::ListParams;
use crate::dtos::upload::{UploadFileCreate, UploadFileUpdate};
use crate::entities::upload::{UploadFile, UploadFileBmc};
use crate::entities::user::UserBmc;
use crate::utils::upload::{generate_oss_path, local, OssProvider, UploadMethod};
use crate::{app_writer::AppResult, utils::upload::init_oss};
use anyhow::anyhow;
use salvo::http::form::FilePart;

pub struct UploadService;
impl UploadService {
    // 获取文件列表
    pub async fn get_list(req: ListParams) -> AppResult<Vec<UploadFile>> {
        let mut limit: u32 = 15;
        let mut offset: u32 = 0;
        if let Some(page) = req.page {
            limit = page.get_limit().unwrap_or(0);
            offset = page.get_offset();
        }
        let mut where_options = Vec::new();
        if let Some(params) = req.params {
            where_options = params;
        }
        let res = UploadFileBmc::get_list(offset, limit, req.options, where_options).await?;
        Ok(res)
    }

    // 上传文件
    pub async fn create_upload(
        file: FilePart,
        target: String,
        u_id: String,
        t_id: String,
        is_temp: bool,
    ) -> AppResult<String> {
        let upload_method = init_oss();
        let path = generate_oss_path(target.as_str());
        let oss_path = if is_temp {
            format!("tmp/{}", path)
        } else {
            path
        };
        match upload_method.add_single(file, &oss_path).await {
            Ok(res) => {
                let obj = UploadFileCreate {
                    owner: u_id,
                    url: res.url.clone(),
                    name: res.filename.clone(),
                    oss_path: res.path.clone(),
                    file_type: res.file_type.clone(),
                    is_temp,
                    t_id,
                    ..Default::default()
                };
                let _ = UploadFileBmc::create(obj).await?;
                Ok(res.url)
            }
            Err(e) => Err(e.into()),
        }
    }

    // 上传多个文件
    pub async fn create_upload_multi(
        files: Vec<FilePart>,
        target: String,
        u_id: String,
        t_id: String,
        is_temp: bool,
    ) -> AppResult<String> {
        let upload_method = init_oss();
        let path = generate_oss_path(target.as_str());
        let oss_path = if is_temp {
            format!("tmp/{}", path)
        } else {
            path
        };
        match upload_method.add_multi(files, &oss_path).await {
            Ok(res) => {
                let mut urls = vec![];
                for file in res.files {
                    let obj = UploadFileCreate {
                        owner: u_id.clone(),
                        url: file.url.clone(),
                        name: file.filename.clone(),
                        oss_path: file.path.clone(),
                        file_type: file.file_type.clone(),
                        is_temp,
                        t_id: t_id.clone(),
                        ..Default::default()
                    };
                    let _ = UploadFileBmc::create(obj).await?;
                    urls.push(file.url.clone());
                }
                Ok(urls.join(","))
            }
            Err(e) => Err(e.into()),
        }
    }

    // 更新上传的文件，如果文件时临时文件，则移动去永久目录
    pub async fn update_upload(u_id: String, t_id: String) -> AppResult<String> {
        let upload = UploadFileBmc::get_by_id(&t_id)
            .await?
            .ok_or_else(|| anyhow!("上传文件不存在"))?;

        if u_id == "system".to_string() {
            return Self::update_file_status(upload).await;
        }

        // 获取用户和上传文件信息
        let user = UserBmc::get_by_id(&u_id)
            .await?
            .ok_or_else(|| anyhow!("用户不存在"))?;

        // 管理员或文件所有者直接通过
        if user.is_admin || u_id == upload.owner {
            return Self::update_file_status(upload).await;
        }

        // 执行文件更新
        Self::update_file_status(upload).await
    }

    // 抽取文件更新逻辑到单独的函数
    async fn update_file_status(upload: UploadFile) -> AppResult<String> {
        let upload_method = init_oss();
        let res = upload_method.make_permanent(&upload.oss_path).await?;

        let update = UploadFileUpdate {
            id: upload.id.to_string(),
            t_id: upload.t_id,
            owner: upload.owner,
            url: res.url.clone(),
            name: upload.name,
            oss_path: res.path.clone(),
            file_type: upload.file_type,
            is_temp: true,
            ..Default::default()
        };

        UploadFileBmc::update(update).await?;
        Ok(res.url)
    }

    // 删除文件
    pub async fn delete(target: String) -> AppResult<String> {
        let upload_method = init_oss();
        match upload_method.delete(&target).await {
            Ok(res) => Ok(res),
            Err(e) => Err(e.into()),
        }
    }

    // 上传临时文件
    pub async fn upload_tmp(file: FilePart, u_id: String, target: String) -> AppResult<String> {
        let upload_method = OssProvider::Local(local::Local);
        let path = generate_oss_path(target.as_str());
        let oss_path = format!("tmp/{}", path);
        match upload_method.add_single(file, &oss_path).await {
            Ok(res) => {
                let obj = UploadFileCreate {
                    owner: u_id,
                    url: res.url.clone(),
                    name: res.filename.clone(),
                    oss_path: res.path.clone(),
                    file_type: res.file_type.clone(),
                    is_temp: true,
                    t_id: "import_order_temp".to_string(),
                    ..Default::default()
                };
                let _ = UploadFileBmc::create(obj).await?;
                Ok(res.path)
            }
            Err(e) => Err(e.into()),
        }
    }
}
