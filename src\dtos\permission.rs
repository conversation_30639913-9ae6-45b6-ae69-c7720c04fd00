use crate::db::{Castable, Creatable, Patchable};
use salvo::prelude::{Extractible, ToSchema};
use serde::{Deserialize, Serialize};
use validator::Validate;

#[derive(Default, Deserialize, Serialize, Debug, Validate, ToSchema)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct PermissionCreate {
    pub name: String,
    pub code: Option<String>,
    pub backup: Option<String>,
    pub method: Option<String>,
    pub path: Option<String>,
    pub group: Option<String>,
}

impl Creatable for PermissionCreate {}

#[derive(Default, Debug, Deserialize, Serialize, Extractible, ToSchema)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct PermissionUpdate {
    #[salvo(extract(source(from = "param")))]
    pub id: String,
    pub name: String,
    pub code: Option<String>,
    pub backup: Option<String>,
    pub method: Option<String>,
    pub path: Option<String>,
    pub group: Option<String>,
}

impl Patchable for PermissionUpdate {}

#[derive(Debug, Deserialize, Serialize, ToSche<PERSON>, De<PERSON>ult, <PERSON>lone)]
pub struct PermissionResponse {
    pub id: String,
    pub name: String,
    pub code: String,
    pub backup: Option<String>,
    pub method: Option<String>,
    pub path: Option<String>,
    pub group: Option<String>,
}

impl Castable for PermissionResponse {}
