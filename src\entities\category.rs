use crate::{
    app_writer::AppResult,
    db::{CountRecord, ListOptions, Patchable},
    dtos::category::{CategoryCreate, CategoryResponse, CategoryUpdate},
};

use crate::db::{Castable, Database, WhereOptions};
use anyhow::anyhow;
use serde::{Deserialize, Serialize};
use surrealdb::RecordId;

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Category {
    pub id: Option<RecordId>,
    pub name: String,
    pub description: Option<String>,
    pub parent: Option<String>,
    pub category_type: String,
    pub order: i32,
    pub children: Option<Vec<Category>>,
}

impl Patchable for Category {}
impl Castable for Category {}

impl Category {
    pub fn response(self) -> CategoryResponse {
        let children = self
            .children
            .map(|children| children.into_iter().map(|child| child.response()).collect());

        CategoryResponse {
            id: self.id.unwrap().to_string(),
            name: self.name,
            order: self.order,
            description: self.description,
            parent: self.parent,
            category_type: self.category_type,
            children,
        }
    }
    pub fn update(new: CategoryUpdate, old: Category) -> Category {
        Category {
            id: None,
            name: if new.name != old.name {
                new.name
            } else {
                old.name
            },
            description: if new.description != old.description {
                new.description
            } else {
                old.description
            },
            parent: if new.parent != old.parent {
                new.parent
            } else {
                old.parent
            },
            category_type: if new.category_type != old.category_type {
                new.category_type
            } else {
                old.category_type
            },
            order: if new.order != old.order {
                new.order
            } else {
                old.order
            },
            children: None,
        }
    }
}

pub struct CategoryBmc;

#[allow(dead_code)]
impl CategoryBmc {
    const ENTITY: &'static str = "category";

    pub async fn get_total(params: Vec<WhereOptions>) -> AppResult<Option<CountRecord>> {
        Database::exec_query_count(Self::ENTITY, params).await
    }

    pub async fn get_list(
        page: u32,
        limit: u32,
        options: Option<ListOptions>,
        params: Vec<WhereOptions>,
    ) -> AppResult<Vec<Category>> {
        Database::exec_query_list(Self::ENTITY, page, limit, options, params).await
    }

    pub async fn get_by_id(id: &str) -> AppResult<Option<Category>> {
        Database::exec_get_by_id(Self::ENTITY, id).await
    }

    pub async fn create(req: CategoryCreate) -> AppResult<String> {
        Database::exec_create(Self::ENTITY, req).await
    }

    pub async fn update(req: CategoryUpdate) -> AppResult<String> {
        let check: Option<Category> = Database::exec_get_by_id(Self::ENTITY, &req.id).await?;
        if check.is_none() {
            return Err(anyhow!("Record not found.").into());
        }
        let old = check.unwrap();
        let tid = old.id.clone().unwrap().to_string();
        let final_tid = if tid.contains(':') {
            tid.split(':').nth(1).unwrap_or(&tid).to_string()
        } else {
            tid
        };
        let obj = Category::update(req, old);
        Database::exec_update(Self::ENTITY, &final_tid, obj).await
    }

    pub async fn delete(id: String) -> AppResult<String> {
        Database::exec_delete(Self::ENTITY, id).await
    }

    pub async fn get_by_query(params: Vec<WhereOptions>) -> AppResult<Option<Category>> {
        Database::exec_get_by_query(Self::ENTITY, params).await
    }
}
