use crate::redis::REDIS_CLIENT;
use redis::{Client, Commands, FromRedisValue, RedisResult, ToRedisArgs};

#[allow(dead_code)]
pub fn set<K: ToRedisArgs, V: ToRedisArgs>(key: K, value: V) -> RedisResult<()> {
    let _: () = Client::set(&mut REDIS_CLIENT.clone(), key, value)?;
    Ok(())
}

#[allow(dead_code)]
pub fn set_ex<K: ToRedisArgs, V: ToRedisArgs>(key: K, value: V, seconds: u64) -> RedisResult<()> {
    let _: () = Client::set_ex(&mut REDIS_CLIENT.clone(), key, value, seconds)?;
    Ok(())
}

#[allow(dead_code)]
pub fn get<T: FromRedisValue, K: ToRedisArgs>(key: K) -> RedisResult<T> {
    let t: T = Client::get(&mut REDIS_CLIENT.clone(), key)?;
    Ok(t)
}

#[allow(dead_code)]
#[allow(unused_must_use)]
pub fn del<K: ToRedisArgs>(key: K) -> RedisResult<()> {
    let _: () = Client::del(&mut REDIS_CLIENT.clone(), key)?;
    Ok(())
}

#[allow(dead_code)]
pub fn hget<T: FromRedisValue, K: ToRedisArgs, F: ToRedisArgs>(key: K, field: F) -> RedisResult<T> {
    let t: T = Client::hget(&mut REDIS_CLIENT.clone(), key, field)?;
    Ok(t)
}

#[allow(dead_code)]
pub fn hset<K: ToRedisArgs, F: ToRedisArgs, V: ToRedisArgs>(
    key: K,
    field: F,
    value: V,
) -> RedisResult<()> {
    let _: () = Client::hset(&mut REDIS_CLIENT.clone(), key, field, value)?;
    Ok(())
}

#[allow(dead_code)]
pub fn hset_nx<K: ToRedisArgs, F: ToRedisArgs, V: ToRedisArgs>(
    key: K,
    field: F,
    value: V,
) -> RedisResult<()> {
    let _: () = Client::hset_nx(&mut REDIS_CLIENT.clone(), key, field, value)?;
    Ok(())
}
#[allow(dead_code)]
#[allow(unused_must_use)]
pub fn exists<K: ToRedisArgs>(key: K) -> bool {
    match Client::exists(&mut REDIS_CLIENT.clone(), key) {
        Ok(exists) => exists,
        Err(_) => false,
    }
}
