use crate::{
    app_writer::{AppR<PERSON><PERSON>, AppWriter},
    db::{CreateParams, ListOptions, ListParams, ListResponse, Page, UpdateParams},
    dtos::category::{CategoryCreate, CategoryResponse, CategoryUpdate},
    services::category::CategoryService,
};
use anyhow::anyhow;
use salvo::oapi::{
    endpoint,
    extract::{JsonBody, PathParam},
};
use salvo::{Router, Writer};

pub fn router() -> Router {
    Router::with_path("category")
        .post(create_category)
        .put(update_category)
        .push(
            Router::with_path("<id>")
                .get(get_category_by_id)
                .delete(delete_category),
        )
        .push(Router::with_path("list").post(get_category_list))
        .push(Router::with_path("tree").post(get_category_tree))
}

#[endpoint(tags("category"))]
async fn get_category_list(req: JsonBody<ListParams>) -> AppWriter<ListResponse<CategoryResponse>> {
    let list = CategoryService::get_list(req.0.clone()).await.unwrap();
    let mut data: Vec<CategoryResponse> = Vec::new();
    for item in list {
        let tmp = item.response();
        data.push(tmp);
    }
    let page = req.0.page.clone().unwrap();
    let total = CategoryService::get_total(req.0.params.clone())
        .await
        .unwrap();
    let res = ListResponse {
        data,
        total,
        page: page.page,
        size: page.limit,
    };

    AppWriter(Ok(res))
}

#[endpoint(tags("category"))]
async fn get_category_tree() -> AppWriter<ListResponse<CategoryResponse>> {
    let params = ListParams {
        page: Some(Page::unlimited()),
        params: None,
        options: Some(ListOptions {
            order_by: Some("order".to_string()),
            desc: Some(false),
        }),
    };
    let list = CategoryService::get_list(params).await.unwrap();
    let mut data: Vec<CategoryResponse> = Vec::new();
    for item in list {
        let tmp = item.response();
        data.push(tmp);
    }
    let tree = CategoryResponse::array_to_tree(data);
    let res = ListResponse {
        data: tree,
        total: 0,
        page: 0,
        size: 0,
    };

    AppWriter(Ok(res))
}

#[endpoint(tags("category"), parameters(("id", description = "category id")))]
async fn get_category_by_id(id: PathParam<String>) -> AppWriter<CategoryResponse> {
    match CategoryService::get_by_id(id.0).await {
        Ok(category) => {
            if let Some(category) = category {
                let res = category.response();
                AppWriter(Ok(res))
            } else {
                AppWriter(Err(anyhow!("category not found").into()))
            }
        }
        Err(e) => AppWriter(Err(e)),
    }
}

#[endpoint(tags("category"))]
async fn create_category(req: JsonBody<CategoryCreate>) -> AppWriter<String> {
    let result = CategoryService::create(CreateParams { data: req.0 }).await;
    AppWriter(result)
}

#[endpoint(tags("category"))]
async fn update_category(req: JsonBody<CategoryUpdate>) -> AppResult<AppWriter<String>> {
    let result = CategoryService::update(UpdateParams { data: req.0 }).await;
    Ok(AppWriter(result))
}

#[endpoint(tags("category"), parameters(("id", description = "category id")))]
async fn delete_category(id: PathParam<String>) -> AppWriter<String> {
    let result = CategoryService::delete(id.0).await;
    AppWriter(result)
}
