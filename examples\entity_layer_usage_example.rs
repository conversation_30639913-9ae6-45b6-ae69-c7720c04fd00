// 实体层优化后的使用示例
// 展示如何使用重构后的更新方法

use crate::{
    db::{UpdateOptions, WhereOptions},
    dtos::sales_order::SalesOrderUpdate,
    entities::sales_order::{SalesOrder, SalesOrderBmc},
    services::sales_order::SalesOrderService,
};
use rust_decimal::Decimal;

/// 展示不同更新方法的使用场景
pub async fn update_methods_comparison() {
    println!("=== 更新方法使用对比 ===\n");

    // 准备测试数据
    let sales_order_update = SalesOrderUpdate {
        id: "sales_order:123".to_string(),
        status: Some("completed".to_string()),
        creator_id: Some("user:1".to_string()),
        updater_id: Some("user:2".to_string()),
        serial: "SO001".to_string(),
        contract_id: Some("contract:456".to_string()),
        amount: Decimal::from_str_exact("1500.00").unwrap(),
        total_payment: Decimal::from_str_exact("1565.00").unwrap(),
        // ... 其他字段
        ..Default::default()
    };

    // 1. 传统方法 - 适用于复杂业务逻辑
    println!("1. 传统方法 (SalesOrderService::update):");
    println!("   适用场景：需要完整的字段验证和转换逻辑");
    match SalesOrderService::update(sales_order_update.clone()).await {
        Ok(result) => println!("   ✓ {}", result),
        Err(e) => println!("   ✗ {}", e),
    }

    // 2. 优化方法 - 适用于性能敏感场景
    println!("\n2. 优化方法 (SalesOrderService::update_optimized):");
    println!("   适用场景：高频更新、性能敏感的场景");
    match SalesOrderService::update_optimized(sales_order_update).await {
        Ok(result) => println!("   ✓ {}", result),
        Err(e) => println!("   ✗ {}", e),
    }

    // 3. 单字段更新 - 适用于简单字段更新
    println!("\n3. 单字段更新 (SalesOrderService::update_field):");
    println!("   适用场景：只需要更新单个字段");
    let params = vec![WhereOptions::new("serial".to_string(), "SO001".to_string())];
    match SalesOrderService::update_field(params, "status", "shipped").await {
        Ok(result) => println!("   ✓ {}", result),
        Err(e) => println!("   ✗ {}", e),
    }

    // 4. 多字段更新 - 适用于自定义字段组合
    println!("\n4. 多字段更新 (SalesOrderService::update_multiple_fields):");
    println!("   适用场景：需要更新特定的字段组合");
    let params = vec![WhereOptions::new("id".to_string(), "sales_order:123".to_string())];
    let update_fields = vec![
        UpdateOptions::new("status".to_string(), "delivered".to_string()),
        UpdateOptions::new("delivery_time".to_string(), "2024-01-20 10:30:00".to_string()),
        UpdateOptions::new("sign_time".to_string(), "2024-01-20 15:45:00".to_string()),
    ];
    match SalesOrderService::update_multiple_fields(params, update_fields).await {
        Ok(result) => println!("   ✓ {}", result),
        Err(e) => println!("   ✗ {}", e),
    }
}

/// 展示实体层方法的直接使用
pub fn entity_layer_usage_example() {
    println!("\n=== 实体层方法使用示例 ===\n");

    // 创建测试数据
    let sales_order_update = SalesOrderUpdate {
        id: "sales_order:456".to_string(),
        status: Some("processing".to_string()),
        serial: "SO002".to_string(),
        amount: Decimal::from_str_exact("2000.00").unwrap(),
        total_payment: Decimal::from_str_exact("2100.00").unwrap(),
        customer: Some("李四".to_string()),
        address: Some("上海市浦东新区".to_string()),
        ..Default::default()
    };

    // 使用实体层方法转换为更新字段列表
    println!("使用 SalesOrder::to_update_options() 转换更新数据:");
    let update_fields = SalesOrder::to_update_options(sales_order_update);
    
    println!("生成的更新字段列表:");
    for (i, field) in update_fields.iter().enumerate() {
        println!("  {}. {} = {}", i + 1, field.field, field.value);
    }
    
    println!("\n生成的SQL片段:");
    let sql_parts: Vec<String> = update_fields.iter().map(|f| f.get_sql()).collect();
    println!("SET {}", sql_parts.join(", "));
}

/// 展示如何为新实体添加优化支持
pub async fn new_entity_optimization_example() {
    println!("\n=== 为新实体添加优化支持示例 ===\n");
    
    println!("步骤1: 在实体的 impl 块中添加 to_update_options 方法");
    println!("```rust");
    println!("impl YourEntity {");
    println!("    pub fn to_update_options(update_data: YourEntityUpdate) -> Vec<UpdateOptions> {");
    println!("        let mut update_fields = Vec::new();");
    println!("        let time_now = Local::now().timestamp_millis();");
    println!("        ");
    println!("        // 处理可选字段");
    println!("        if let Some(field) = update_data.optional_field {");
    println!("            update_fields.push(UpdateOptions::new(\"optional_field\".to_string(), field));");
    println!("        }");
    println!("        ");
    println!("        // 处理必需字段");
    println!("        update_fields.push(UpdateOptions::new(\"required_field\".to_string(), update_data.required_field));");
    println!("        ");
    println!("        // 处理数值字段");
    println!("        update_fields.push(UpdateOptions::new(\"amount\".to_string(), update_data.amount.to_string()));");
    println!("        ");
    println!("        // 自动更新时间戳");
    println!("        update_fields.push(UpdateOptions::new(\"updated_at\".to_string(), time_now.to_string()));");
    println!("        ");
    println!("        update_fields");
    println!("    }");
    println!("}}");
    println!("```");
    
    println!("\n步骤2: 在BMC的 impl 块中添加 update_optimized 方法");
    println!("```rust");
    println!("impl YourEntityBmc {");
    println!("    pub async fn update_optimized(entity_data: YourEntityUpdate) -> AppResult<String> {");
    println!("        // 检查记录是否存在");
    println!("        let check: Option<YourEntity> =");
    println!("            Database::exec_get_by_id(Self::ENTITY, &entity_data.id).await?;");
    println!("        if check.is_none() {");
    println!("            return Err(AppError::AnyHow(anyhow!(\"Entity not found.\")));");
    println!("        }");
    println!("        ");
    println!("        // 构建查询条件");
    println!("        let params = vec![WhereOptions::new(\"id\".to_string(), entity_data.id.clone())];");
    println!("        ");
    println!("        // 使用实体层的方法转换为更新字段列表");
    println!("        let update_fields = YourEntity::to_update_options(entity_data);");
    println!("        ");
    println!("        // 执行多字段更新");
    println!("        Self::update_multiple_fields(params, update_fields).await");
    println!("    }");
    println!("}}");
    println!("```");
    
    println!("\n步骤3: 在Service层添加对应的包装方法");
    println!("```rust");
    println!("impl YourEntityService {");
    println!("    pub async fn update_optimized(req: YourEntityUpdate) -> AppResult<String> {");
    println!("        YourEntityBmc::update_optimized(req).await?;");
    println!("        Ok(\"Entity updated (optimized)\".to_string())");
    println!("    }");
    println!("}}");
    println!("```");
}

/// 性能测试示例
pub async fn performance_test_example() {
    println!("\n=== 性能测试示例 ===\n");
    
    let test_data = SalesOrderUpdate {
        id: "sales_order:test".to_string(),
        status: Some("test".to_string()),
        serial: "SO_TEST".to_string(),
        amount: Decimal::from_str_exact("1000.00").unwrap(),
        total_payment: Decimal::from_str_exact("1000.00").unwrap(),
        ..Default::default()
    };
    
    // 测试传统方法
    println!("测试传统方法性能:");
    let start = std::time::Instant::now();
    for i in 0..100 {
        let mut test_data_clone = test_data.clone();
        test_data_clone.id = format!("sales_order:test_{}", i);
        // 这里只是模拟，实际测试需要真实的数据库连接
        // let _ = SalesOrderService::update(test_data_clone).await;
    }
    let traditional_duration = start.elapsed();
    println!("传统方法 100次更新耗时: {:?}", traditional_duration);
    
    // 测试优化方法
    println!("\n测试优化方法性能:");
    let start = std::time::Instant::now();
    for i in 0..100 {
        let mut test_data_clone = test_data.clone();
        test_data_clone.id = format!("sales_order:test_{}", i);
        // 这里只是模拟，实际测试需要真实的数据库连接
        // let _ = SalesOrderService::update_optimized(test_data_clone).await;
    }
    let optimized_duration = start.elapsed();
    println!("优化方法 100次更新耗时: {:?}", optimized_duration);
    
    if traditional_duration > optimized_duration {
        let improvement = traditional_duration - optimized_duration;
        let percentage = (improvement.as_nanos() as f64 / traditional_duration.as_nanos() as f64) * 100.0;
        println!("\n性能提升: {:?} ({:.1}%)", improvement, percentage);
    }
}

/// 主函数示例
pub async fn run_all_examples() {
    println!("🚀 实体层优化使用示例\n");
    
    // 运行各种示例
    update_methods_comparison().await;
    entity_layer_usage_example();
    new_entity_optimization_example().await;
    performance_test_example().await;
    
    println!("\n✅ 所有示例运行完成！");
}
