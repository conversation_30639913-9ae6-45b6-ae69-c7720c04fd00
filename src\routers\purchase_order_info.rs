use crate::{
    app_writer::{App<PERSON><PERSON><PERSON>, AppWriter},
    db::{ListParams, ListResponse, WhereOptions},
    dtos::purchase_order_info::{
        PurchaseOrderInfoCreate, PurchaseOrderInfoResponse, PurchaseOrderInfoUpdate,
    },
    services::purchase_order_info::PurchaseOrderInfoService,
};
use salvo::{
    oapi::{
        endpoint,
        extract::{JsonBody, PathParam},
    },
    Request,
};
use salvo::{Router, Writer};

pub fn router() -> Router {
    Router::with_path("purchase_order_info")
        .post(create_purchase_order_info)
        .put(update_purchase_order_info)
        .push(
            Router::with_path("<id>")
                .get(get_purchase_order_info_by_id)
                .delete(delete_purchase_order_info),
        )
        .push(Router::with_path("list").post(get_purchase_order_info_list))
}

#[endpoint(tags("purchase_order_info"))]
async fn get_purchase_order_info_list(
    req: JsonBody<ListParams>,
) -> AppWriter<ListResponse<PurchaseOrderInfoResponse>> {
    let list = PurchaseOrderInfoService::get_list(req.0.clone())
        .await
        .unwrap();

    let mut data: Vec<PurchaseOrderInfoResponse> = Vec::new();
    for item in list {
        let tmp = item.response().await;
        data.push(tmp);
    }
    let page = req.0.page.clone().unwrap();
    let total = PurchaseOrderInfoService::get_total(req.0.params.clone())
        .await
        .unwrap();
    let res = ListResponse {
        data,
        total,
        page: page.page,
        size: page.limit,
    };
    AppWriter(Ok(res))
}

#[endpoint(tags("purchase_order_info"), parameters(("id", description = "purchase_order_info id for params")))]
async fn get_purchase_order_info_by_id(
    id: PathParam<String>,
) -> AppWriter<PurchaseOrderInfoResponse> {
    match PurchaseOrderInfoService::get_by_id(id.0).await {
        Ok(purchase_order_info) => {
            let res = purchase_order_info.response().await;
            AppWriter(Ok(res))
        }
        Err(e) => AppWriter(Err(e)),
    }
}

#[endpoint(tags("purchase_order_info"))]
async fn create_purchase_order_info(req: JsonBody<PurchaseOrderInfoCreate>) -> AppWriter<String> {
    let result = PurchaseOrderInfoService::create(req.0).await;
    AppWriter(result)
}

#[endpoint(tags("purchase_order_info"))]
async fn update_purchase_order_info(
    req: JsonBody<PurchaseOrderInfoUpdate>,
) -> AppResult<AppWriter<String>> {
    let result = PurchaseOrderInfoService::update(req.0).await;
    Ok(AppWriter(result))
}

#[endpoint(tags("purchase_order_info"), parameters(("id", description = "user id")))]
async fn delete_purchase_order_info(id: PathParam<String>) -> AppWriter<String> {
    let result = PurchaseOrderInfoService::delete(id.0).await;
    AppWriter(result)
}
