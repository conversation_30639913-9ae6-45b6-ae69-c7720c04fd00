use std::str::FromStr;

use crate::{
    app_writer::AppResult,
    db::{
        Castable, CountRecord, Creatable, Database, ListOptions, Patchable, RelateParams,
        WhereOptions,
    },
    dtos::import_record::{ImportRecordCreate, ImportRecordResponse, ImportRecordUpdate},
};
use chrono::Local;
use serde::{Deserialize, Serialize};
use surrealdb::RecordId;

#[derive(Debug, Serialize, Deserialize)]
pub struct ImportRecord {
    pub id: Option<RecordId>,
    pub serial: String,
    pub contract_id: String, // 项目ID
    pub contract_name: Option<String>,
    pub source: String,     // 获取数据的来源
    pub file_type: Option<String>,
    pub save_dir: String,
    pub file_name: String,
    pub file_link: String,
    // 导入总数量
    pub total_count: i64,
    // 导入的新条目
    pub new_count: i64,
    // 导入的更新条目
    pub update_count: i64,
    // 导入的无变化条目
    pub no_change_count: i64,
    // 导入成功条目
    pub success_count: i64,
    // 导入失败条目
    pub fail_count: i64,
    pub status: String,
    pub created_at: i64,
    pub updated_at: i64,
}

impl Creatable for ImportRecord {}
impl Patchable for ImportRecord {}
impl Castable for ImportRecord {}

impl ImportRecord {
    /// 将 ImportRecord 实体转换为响应 DTO
    /// # 返回值
    /// * `ImportRecordResponse` - 转换后的响应数据
    pub fn response(self) -> ImportRecordResponse {
        ImportRecordResponse {
            id: self.id.unwrap().to_string(),
            serial: Some(self.serial),
            contract_id: self.contract_id,
            contract_name: self.contract_name,
            source: self.source,
            file_type: self.file_type,
            save_dir: self.save_dir,
            file_name: self.file_name,
            file_link: self.file_link,
            total_count: self.total_count,
            new_count: self.new_count,
            update_count: self.update_count,
            no_change_count: self.no_change_count,
            success_count: self.success_count,
            fail_count: self.fail_count,
            status: self.status,
            created_at: self.created_at,
            updated_at: self.updated_at,
        }
    }

    /// 创建新的导入记录实体
    /// # 参数
    /// * `obj` - 导入记录创建数据
    /// # 返回值
    /// * `ImportRecord` - 新创建的导入记录实体
    pub fn create(obj: ImportRecordCreate) -> ImportRecord {
        let time_now = Local::now().timestamp_millis();
        ImportRecord {
            id: None,
            serial: obj.serial.unwrap_or_default(),
            contract_id: obj.contract_id,
            contract_name: obj.contract_name,
            source: obj.source,
            file_type: obj.file_type,
            save_dir: obj.save_dir,
            file_name: obj.file_name,
            file_link: obj.file_link,
            total_count: obj.total_count,
            new_count: obj.new_count,
            update_count: obj.update_count,
            no_change_count: obj.no_change_count,
            success_count: obj.success_count,
            fail_count: obj.fail_count,
            status: obj.status,
            created_at: time_now,
            updated_at: time_now,
        }
    }

    /// 更新导入记录实体
    /// # 参数
    /// * `new` - 新的导入记录更新数据
    /// * `old` - 原有的导入记录数据
    /// # 返回值
    /// * `ImportRecord` - 更新后的导入记录实体
    pub fn update(new: ImportRecordUpdate, old: ImportRecord) -> ImportRecord {
        let time_now = Local::now().timestamp_millis();
        
        ImportRecord {
            id: old.id.clone(),
            serial: new.serial.unwrap_or_default(),
            contract_id: new.contract_id,
            contract_name: new.contract_name,
            source: new.source,
            file_type: new.file_type,
            save_dir: new.save_dir,
            file_name: new.file_name,
            file_link: new.file_link,
            total_count: new.total_count,
            new_count: new.new_count,
            update_count: new.update_count,
            no_change_count: new.no_change_count,
            success_count: new.success_count,
            fail_count: new.fail_count,
            status: new.status,
            created_at: old.created_at, // 保持原创建时间
            updated_at: time_now,       // 更新修改时间
        }
    }
}

pub struct ImportRecordBmc;

impl ImportRecordBmc {
    const ENTITY: &'static str = "import_record";

    /// 获取导入记录列表
    /// # 参数
    /// * `page` - 页码
    /// * `limit` - 每页数量
    /// * `options` - 列表选项
    /// * `params` - 查询条件
    /// # 返回值
    /// * `AppResult<Vec<ImportRecord>>` - 导入记录列表
    pub async fn get_list(
        page: u32,
        limit: u32,
        options: Option<ListOptions>,
        params: Vec<WhereOptions>,
    ) -> AppResult<Vec<ImportRecord>> {
        Database::exec_query_list(Self::ENTITY, page, limit, options, params).await
    }

    /// 获取导入记录总数
    /// # 参数
    /// * `params` - 查询条件
    /// # 返回值
    /// * `AppResult<Option<CountRecord>>` - 记录总数
    pub async fn get_total(params: Vec<WhereOptions>) -> AppResult<Option<CountRecord>> {
        Database::exec_query_count(Self::ENTITY, params).await
    }

    /// 根据查询条件获取单个导入记录
    /// # 参数
    /// * `params` - 查询条件
    /// # 返回值
    /// * `AppResult<Option<ImportRecord>>` - 导入记录
    pub async fn get_by_query(params: Vec<WhereOptions>) -> AppResult<Option<ImportRecord>> {
        Database::exec_get_by_query(Self::ENTITY, params).await
    }

    /// 根据ID获取导入记录
    /// # 参数
    /// * `id` - 记录ID
    /// # 返回值
    /// * `AppResult<Option<ImportRecord>>` - 导入记录
    pub async fn get_by_id(id: &str) -> AppResult<Option<ImportRecord>> {
        Database::exec_get_by_id(Self::ENTITY, id).await
    }

    /// 创建新的导入记录
    /// # 参数
    /// * `import_record` - 导入记录创建数据
    /// # 返回值
    /// * `AppResult<String>` - 创建结果，成功时返回记录ID
    pub async fn create(import_record: ImportRecordCreate) -> AppResult<String> {
        let obj = ImportRecord::create(import_record);
        Database::exec_create(Self::ENTITY, obj).await
    }

    /// 更新导入记录
    /// # 参数
    /// * `import_record` - 导入记录更新数据
    /// # 返回值
    /// * `AppResult<String>` - 更新结果，成功时返回记录ID
    pub async fn update(import_record: ImportRecordUpdate) -> AppResult<String> {
        let tid = import_record.id.clone();
        
        // 获取原有记录
        let old_record = Self::get_by_id(&tid).await?;
        if let Some(old) = old_record {
            // 使用 ImportRecord::update 方法创建更新后的实体
            let updated_record = ImportRecord::update(import_record, old);
            Database::exec_update(Self::ENTITY, &tid, updated_record).await
        } else {
            Err(anyhow::anyhow!("记录不存在: {}", tid).into())
        }
    }

    /// 删除导入记录
    /// # 参数
    /// * `id` - 记录ID
    /// # 返回值
    /// * `AppResult<String>` - 删除结果
    pub async fn delete(id: String) -> AppResult<String> {
        Database::exec_delete(Self::ENTITY, id).await
    }

    /// 关联导入记录到其他项目
    /// # 参数
    /// * `params` - 关联参数
    /// * `state` - 状态
    /// # 返回值
    /// * `AppResult<String>` - 关联结果
    pub async fn relate_to_item(params: RelateParams, state: &str) -> AppResult<String> {
        let from = vec![params.from];
        Database::exec_relate_batch(state, from, params.to).await
    }
}
