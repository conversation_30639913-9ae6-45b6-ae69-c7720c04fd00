use super::sales_order::SalesOrderService;
use crate::{
    app_writer::AppResult,
    db::{ListParams, RelateParams, UpdateOptions, WhereOptions},
    dtos::repayment::{RelateOrders, RepaymentCreate, RepaymentUpdate},
    entities::repayment::{Repayment, RepaymentBmc},
};
use anyhow::anyhow;

pub struct RepaymentService;

impl RepaymentService {
    /// 获取还款计划总数
    /// # 参数
    /// * `req` - 查询条件，可选
    pub async fn get_total(req: Option<Vec<WhereOptions>>) -> AppResult<u32> {
        let mut where_options = Vec::new();
        if let Some(params) = req {
            where_options = params;
        }
        match RepaymentBmc::get_total(where_options).await {
            Ok(count) => {
                if let Some(count) = count {
                    Ok(count.count)
                } else {
                    Ok(0)
                }
            }
            Err(_) => Ok(0),
        }
    }

    /// 获取还款计划列表
    /// # 参数
    /// * `req` - 列表查询参数
    pub async fn get_list(req: ListParams) -> AppResult<Vec<Repayment>> {
        let mut limit: u32 = 15;
        let mut offset: u32 = 0;
        if let Some(page) = req.page {
            limit = page.get_limit().unwrap_or(0);
            offset = page.get_offset();
        }
        let mut where_options = Vec::new();
        if let Some(params) = req.params {
            where_options = params;
        }
        let res = RepaymentBmc::get_list(offset, limit, req.options, where_options).await?;
        Ok(res)
    }

    /// 根据查询条件获取单个还款计划
    /// # 参数
    /// * `params` - 查询条件
    pub async fn get_by_query(params: Vec<WhereOptions>) -> AppResult<Repayment> {
        match RepaymentBmc::get_by_query(params).await {
            Ok(res) => {
                if let Some(res) = res {
                    Ok(res)
                } else {
                    Err(anyhow!("Repayment not found.").into())
                }
            }
            Err(e) => Err(e),
        }
    }

    /// 根据ID获取还款计划
    /// # 参数
    /// * `id` - 还款计划ID
    pub async fn get_by_id(id: String) -> AppResult<Repayment> {
        match RepaymentBmc::get_by_id(&id).await {
            Ok(res) => {
                if let Some(res) = res {
                    Ok(res)
                } else {
                    Err(anyhow!("Repayment not found.").into())
                }
            }
            Err(e) => Err(e),
        }
    }

    /// 创建新的还款计划
    /// # 参数
    /// * `req` - 创建还款计划的数据
    pub async fn create(req: RepaymentCreate) -> AppResult<String> {
        let res = RepaymentBmc::create(req).await?;
        Ok(res)
    }

    /// 更新还款计划
    /// # 参数
    /// * `req` - 更新还款计划的数据
    pub async fn update(req: RepaymentUpdate) -> AppResult<String> {
        RepaymentBmc::update(req).await?;
        Ok("Repayment updated".to_string())
    }

    /// 删除还款计划
    /// # 参数
    /// * `id` - 要删除的还款计划ID
    pub async fn delete(id: String) -> AppResult<String> {
        let res = RepaymentBmc::delete(id.clone()).await?;
        let params = vec![WhereOptions::new("repayment_id".to_string(), id.clone())];
        // 更新关联的订单信息
        SalesOrderService::update_field(
            params,
            "repayment_id",
            "repayment:None",
        )
        .await?;   
        Ok(res)
    }

    pub async fn relate_orders(req: RelateOrders) -> AppResult<String> {
        // 使用Surreal DB的关联特性，不修改sales_order的字段了，这样后续可以方便删除(?)
        RepaymentBmc::relate_to_item(
            RelateParams {
                from: req.id.clone(),
                to: req.ids.clone(),
            },
            "has_order",
        )
        .await?;

        // 暂时先保留字段的更新
        let order_ids = req.ids.clone();
        SalesOrderService::update_batch_by_ids(
            order_ids.clone(),
            vec![UpdateOptions::new(
                "repayment_id".to_string(),
                req.id.clone(),
            )],
        )
        .await?;    

        // 根据订单ID统计订单总额
        let sum_params = vec![WhereOptions::new(
            "ids".to_string(),
            order_ids.clone().join(","),
        )];
        let total_payment = SalesOrderService::sum_field_fast(sum_params, "total_payment").await?;
        // 更新还款计划的总额
        let update_params = vec![WhereOptions::new("id".to_string(), req.id.clone())];
        RepaymentBmc::update_field(update_params, "target_amount", &total_payment).await?;
        Ok("Repayment relate orders".to_string())
    }
}
