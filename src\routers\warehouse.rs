use crate::{
    app_writer::{AppR<PERSON>ult, AppWriter},
    db::{ListParams, ListResponse, Page},
    dtos::warehouse::{WarehouseCreate, WarehouseResponse, WarehouseUpdate},
    services::warehouse::WarehouseService,
};
use salvo::{
    oapi::{
        endpoint,
        extract::{JsonBody, PathParam},
    },
    Request,
};
use salvo::{Router, Writer};

pub fn router() -> Router {
    Router::with_path("warehouse")
        .post(create_warehouse)
        .put(update_warehouse)
        .push(
            Router::with_path("<id>")
                .get(get_warehouse_by_id)
                .delete(delete_warehouse),
        )
        .push(Router::with_path("list").post(get_warehouse_list))
}

#[endpoint(tags("warehouse"))]
async fn get_warehouse_list(
    req: JsonBody<ListParams>,
) -> AppWriter<ListResponse<WarehouseResponse>> {
    let page_params = req.0.page.clone();
    let mut page = Page::default();
    if page_params.is_some() {
        page = page_params.unwrap();
    };
    let list = WarehouseService::get_list(req.0.clone()).await.unwrap();

    let mut data: Vec<WarehouseResponse> = Vec::new();
    for item in list {
        let tmp = item.response();
        data.push(tmp);
    }
    let total = WarehouseService::get_total(req.0.params).await.unwrap();
    let res = ListResponse {
        data,
        total,
        page: page.page,
        size: page.limit,
    };
    AppWriter(Ok(res))
}

#[endpoint(tags("warehouse"), parameters(("id", description = "warehouse id for params")))]
async fn get_warehouse_by_id(id: PathParam<String>) -> AppWriter<WarehouseResponse> {
    match WarehouseService::get_by_id(id.0).await {
        Ok(warehouse) => {
            let res = warehouse.response();
            AppWriter(Ok(res))
        }
        Err(e) => AppWriter(Err(e)),
    }
}

#[endpoint(tags("warehouse"))]
async fn create_warehouse(req: JsonBody<WarehouseCreate>) -> AppWriter<String> {
    let result = WarehouseService::create(req.0).await;
    AppWriter(result)
}

#[endpoint(tags("warehouse"))]
async fn update_warehouse(req: JsonBody<WarehouseUpdate>) -> AppResult<AppWriter<String>> {
    let result = WarehouseService::update(req.0).await;
    Ok(AppWriter(result))
}

#[endpoint(tags("warehouse"), parameters(("id", description = "user id")))]
async fn delete_warehouse(id: PathParam<String>) -> AppWriter<String> {
    let result = WarehouseService::delete(id.0).await;
    AppWriter(result)
}
