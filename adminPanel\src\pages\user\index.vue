<template>
  <base-content scrollable>
    <div class="row q-gutter-md q-ma-md">
      <div class="col">
        <div class="items-center row q-gutter-md" style="margin-bottom: 10px">
          <q-input v-model="queryParams.username" style="width: 20%" :label="$t('admin.Username')" />
          <q-input v-model="queryParams.realname" style="width: 20%" :label="$t('admin.RealName')" />
          <q-btn color="primary" :label="$t('admin.Search')" @click="handleSearch" />
          <q-btn color="primary" :label="$t('admin.Reset')" @click="resetSearch" />
        </div>
        <q-table v-model:pagination="pagination" row-key="id" separator="cell" :rows="tableData" :columns="columns"
          :rows-per-page-options="pageOptions" :loading="loading" @request="onRequest">
          <template #top="props">
            <q-btn color="primary" :label="$t('admin.Add') + $t('admin.User')" @click="showAddForm" />
            <q-space />
            <q-btn flat round dense :icon="props.inFullscreen ? 'fullscreen_exit' : 'fullscreen'" class="q-ml-md"
              @click="props.toggleFullscreen" />
          </template>

          <template #body-cell-id="props">
            <q-td :props="props">
              {{ props.row.id.split(':')[1] }}
            </q-td>
          </template>

          <template #body-cell-is_admin="props">
            <q-td :props="props">
              <q-chip v-if="props.row.is_admin" color="positive" class="text-white">是</q-chip>
              <q-chip v-else>否</q-chip>
            </q-td>
          </template>

          <template #body-cell-is_active="props">
            <q-td :props="props">
              <q-chip v-if="props.row.is_active" color="positive" class="text-white">启用</q-chip>
              <q-chip v-else>停用</q-chip>
            </q-td>
          </template>


          <template #body-cell-created_at="props">
            <q-td :props="props">
              {{ showDateTime(props.row.created_at) }}
            </q-td>
          </template>

          <template #body-cell-actions="props">
            <q-td :props="props">
              <div class="q-gutter-xs">
                <q-btn-group>
                  <q-btn size="sm" color="primary" :label="$t('admin.Edit')" @click="showEditForm(props.row)" />
                  <q-btn v-if="props.row.id !== 1" color="negative" size="sm" :label="$t('admin.Delete')"
                    @click="handleDelete(props.row)" />
                </q-btn-group>
              </div>
            </q-td>
          </template>
        </q-table>
        <RecordDetail ref="recordDetailDialog" @handleFinish="handleFinish" />
      </div>
    </div>
  </base-content>
</template>

<script setup>
defineOptions({ name: "UserList" });
import { useQuasar } from "quasar";
import BaseContent from "src/components/BaseContent/BaseContent.vue";
import useTableData from "src/composables/useTableData";
import RecordDetail from "src/pages/user/modules/recordDetail.vue";
import { computed, onMounted } from "vue";
import { FormatDateTime } from "src/utils/date";
import { useI18n } from "vue-i18n";

const url = {
  list: "/api/user/list",
  item: "/api/user",
  create: "/api/user",
  edit: "/api/user",
  delete: "/api/user",
};

const {
  pagination,
  queryParams,
  pageOptions,
  loading,
  tableData,
  recordDetailDialog,
  showAddForm,
  showEditForm,
  onRequest,
  getTableData,
  handleSearch,
  handleFinish,
  handleDelete,
} = useTableData(url);

onMounted(async () => {
  pagination.value.sortBy = "created_at";
  pagination.value.descending = false;
  await getTableData();
});

const $q = useQuasar();
const { t } = useI18n();

const showDateTime = computed(() => {
  return (datetime) => {
    return FormatDateTime(datetime);
  };
});
const columns = computed(() => {
  return [
    { name: "id", align: "center", label: t("admin.Id"), field: "id" },
    {
      name: "username",
      align: "center",
      label: t("admin.Username"),
      field: "username",
    },
    {
      name: "realname",
      align: "center",
      label: t("admin.RealName"),
      field: "realname",
    },
    { name: "is_admin", align: "center", label: t("admin.Admin"), field: "is_admin" },
    {
      name: "is_active",
      align: "center",
      label: t("admin.Status"),
      field: "is_active",
    },
    {
      name: "created_at",
      align: "center",
      label: t("admin.CreatedAt"),
      field: "created_at",
    },
    {
      name: "actions",
      align: "center",
      label: t("admin.Actions"),
      field: "actions",
    },
  ];
});
</script>
