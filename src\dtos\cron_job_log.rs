use crate::db::{Castable, Creatable, Patchable};
use salvo::prelude::{Extractible, ToSchema};
use serde::{Deserialize, Serialize};
use validator::Validate;

#[derive(Default, Clone, Deserialize, Serialize, Debug, Validate, ToSchema)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct CronJobLogResponse {
    pub id: String,
    pub job_id: String,
    pub job_uuid: String,
    pub start_time: String,
    pub end_time: String,
    pub status: String,
    pub result: Option<String>,
    pub error: Option<String>,
    pub created_at: String,
    pub updated_at: String,
}

impl Castable for CronJobLogResponse {}
