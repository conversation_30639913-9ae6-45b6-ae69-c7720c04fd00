use std::str::FromStr;

use crate::{
    app_writer::AppResult,
    db::{
        Castable, CountRecord, Creatable, Database, ListOptions, Patchable, RelateParams,
        WhereOptions,
    },
    dtos::repayment_log::{RepaymentLogCreate, RepaymentLogResponse, RepaymentLogUpdate},
};
use chrono::Local;
use serde::{Deserialize, Serialize};
use surrealdb::RecordId;
use anyhow::anyhow;

#[derive(Debug, Serialize, Deserialize)]
pub struct RepaymentLog {
    pub id: Option<RecordId>,
    pub parent_id: String,       // 还款计划ID
    pub total: String,           // 本次还款总额
    pub date: String,            // 本次还款日期
    pub profit: String,          // 还款的利息部分
    pub principal: String,       // 还款的本金部分
    pub status: String,          // 还款状态
    pub remark: Option<String>,  // 还款备注
    pub repayer: Option<String>, // 还款人
    pub created_at: i64,
    pub updated_at: i64,
}

impl Creatable for RepaymentLog {}
impl Patchable for RepaymentLog {}
impl Castable for RepaymentLog {}

impl RepaymentLog {
    /// 将 RepaymentLog 实体转换为响应 DTO
    pub fn response(self) -> RepaymentLogResponse {
        RepaymentLogResponse {
            id: self.id.unwrap().to_string(),
            parent_id: self.parent_id,
            total: self.total,
            date: self.date,
            profit: self.profit,
            principal: self.principal,
            status: self.status,
            remark: self.remark,
            repayer: self.repayer,
            created_at: self.created_at,
            updated_at: self.updated_at,
        }
    }

    /// 从创建 DTO 创建新的 RepaymentLog 实体
    pub fn create(obj: RepaymentLogCreate) -> RepaymentLog {
        let time_now: i64 = Local::now().timestamp_millis();
        RepaymentLog {
            id: None,
            parent_id: obj.parent_id,
            total: obj.total,
            date: obj.date,
            profit: obj.profit,
            principal: obj.principal,
            status: obj.status,
            remark: obj.remark,
            repayer: obj.repayer,
            created_at: time_now,
            updated_at: time_now,
        }
    }

    /// 从更新 DTO 更新 RepaymentLog 实体
    pub fn update(mut self, obj: RepaymentLogUpdate) -> RepaymentLog {
        let time_now: i64 = Local::now().timestamp_millis();
        
        // 更新所有字段
        self.parent_id = obj.parent_id;
        self.total = obj.total;
        self.date = obj.date;
        self.profit = obj.profit;
        self.principal = obj.principal;
        self.status = obj.status;
        self.remark = obj.remark;
        self.repayer = obj.repayer;
        
        // 更新时间戳
        self.updated_at = time_now;
        
        self
    }
}

pub struct RepaymentLogBmc;

impl RepaymentLogBmc {
    const ENTITY: &'static str = "repayment_log";

    pub async fn get_list(
        page: u32,
        limit: u32,
        options: Option<ListOptions>,
        params: Vec<WhereOptions>,
    ) -> AppResult<Vec<RepaymentLog>> {
        Database::exec_query_list(Self::ENTITY, page, limit, options, params).await
    }

    pub async fn get_total(params: Vec<WhereOptions>) -> AppResult<Option<CountRecord>> {
        Database::exec_query_count(Self::ENTITY, params).await
    }

    pub async fn get_by_query(params: Vec<WhereOptions>) -> AppResult<Option<RepaymentLog>> {
        Database::exec_get_by_query(Self::ENTITY, params).await
    }

    pub async fn get_by_id(id: &str) -> AppResult<Option<RepaymentLog>> {
        Database::exec_get_by_id(Self::ENTITY, id).await
    }

    pub async fn create(repayment_log: RepaymentLogCreate) -> AppResult<String> {
        let obj = RepaymentLog::create(repayment_log);
        Database::exec_create(Self::ENTITY, obj).await
    }

    pub async fn update(repayment_log: RepaymentLogUpdate) -> AppResult<String> {
        let check: Option<RepaymentLog> = Database::exec_get_by_id(Self::ENTITY, &repayment_log.id.clone())
            .await?;
        if check.is_none() {
            return Err(anyhow!("RepaymentLog not found.").into());
        }
        let old = check.unwrap();
        let tid = old.id.clone().unwrap().to_string();
        let final_tid = if tid.contains(':') {
            tid.split(':').nth(1).unwrap_or(&tid).to_string()
        } else {
            tid.clone()
        };
        let obj = RepaymentLog::update(old, repayment_log);
        Database::exec_update(Self::ENTITY, &final_tid, obj).await
    }

    pub async fn delete(id: String) -> AppResult<String> {
        Database::exec_delete(Self::ENTITY, id).await
    }

    pub async fn relate_to_item(params: RelateParams, state: &str) -> AppResult<String> {
        let from = vec![params.from];
        Database::exec_relate_batch(state, from, params.to).await
    }
}
