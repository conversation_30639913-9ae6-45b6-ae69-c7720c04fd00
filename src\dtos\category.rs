use crate::db::{Castable, Creatable, Patchable};
use salvo::prelude::{Extractible, ToSchema};
use serde::{Deserialize, Serialize};
use std::collections::{HashMap, VecDeque};
use validator::Validate;

#[derive(Default, Clone, Deserialize, Serial<PERSON>, Debug, Validate, ToSchema)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct CategoryCreate {
    pub name: String,
    pub description: Option<String>,
    pub parent: Option<String>,
    pub category_type: String,
    pub order: i32,
}

impl Creatable for CategoryCreate {}

#[derive(Default, Debug, Deserialize, Serialize, Extractible, ToSchema)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct CategoryUpdate {
    pub id: String,
    pub name: String,
    pub description: Option<String>,
    pub parent: Option<String>,
    pub category_type: String,
    pub order: i32,
}

impl Patchable for CategoryUpdate {}

#[derive(Debug, Deserialize, Serialize, To<PERSON>che<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>)]
pub struct CategoryResponse {
    pub id: String,
    pub name: String,
    pub description: Option<String>,
    pub parent: Option<String>,
    pub category_type: String,
    pub order: i32,
    pub children: Option<Vec<CategoryResponse>>,
}

impl Castable for CategoryResponse {}

#[allow(dead_code)]
impl CategoryResponse {
    pub fn array_to_tree(items: Vec<CategoryResponse>) -> Vec<CategoryResponse> {
        // 内部帮助函数，处理节点更新
        fn update_node_in_parent(
            result: &mut Vec<CategoryResponse>,
            current_node: &CategoryResponse,
            parent_id: &str,
        ) {
            if let Some(parent) = result.iter_mut().find(|node| node.name == parent_id) {
                if let Some(children) = &mut parent.children {
                    if let Some(existing) =
                        children.iter_mut().find(|c| c.name == current_node.name)
                    {
                        *existing = current_node.clone();
                    }
                }
            }
        }

        // 初始化数据结构
        let mut node_map: HashMap<String, Vec<CategoryResponse>> = HashMap::new();
        let mut processing = HashMap::new(); // 记录节点处理状态
        let mut result = Vec::new();
        let mut queue = VecDeque::new();

        // 第一步：分类节点
        for item in items {
            let parent_id = item.parent.clone().unwrap_or_default();
            processing.insert(item.name.clone(), true);

            if parent_id.is_empty() || parent_id == "''" {
                result.push(item.clone());
                queue.push_back(item);
            } else {
                node_map.entry(parent_id).or_default().push(item);
            }
        }

        // 第二步：构建树结构
        while let Some(mut current) = queue.pop_front() {
            // 处理子节点
            if let Some(children) = node_map.get(&current.name) {
                let mut child_nodes = Vec::new();

                for child in children {
                    child_nodes.push(child.clone());

                    if node_map.contains_key(&child.name) && processing[&child.name] {
                        queue.push_back(child.clone());
                    }
                }

                current.children = Some(child_nodes);
            } else {
                processing.insert(current.name.clone(), false);
            }

            // 更新当前节点
            if let Some(node) = result.iter_mut().find(|n| n.name == current.name) {
                *node = current.clone();
            }

            // 检查子节点状态并更新父节点
            if let Some(children) = &current.children {
                let all_processed = children
                    .iter()
                    .all(|child| !processing.get(&child.name).unwrap_or(&false));

                if !all_processed {
                    processing.insert(current.name.clone(), false);

                    // 更新父节点中的信息
                    if let Some(parent_id) = &current.parent {
                        update_node_in_parent(&mut result, &current, parent_id);
                    }
                } else {
                    queue.push_back(current);
                }
            }
        }

        // 最终排序
        result.sort_by_key(|node| node.order);
        result
    }
}
