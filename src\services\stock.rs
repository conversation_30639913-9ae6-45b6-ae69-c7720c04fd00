use anyhow::anyhow;

use crate::{
    app_writer::AppResult,
    db::{ListParams, WhereOptions},
    dtos::stock::{StockCreate, StockUpdate},
    entities::stock::{Stock, StockBmc},
};

pub struct StockService;
impl StockService {
    pub async fn get_total(req: Option<Vec<WhereOptions>>) -> AppResult<u32> {
        let mut where_options = Vec::new();
        if let Some(params) = req {
            where_options = params;
        }
        match StockBmc::get_total(where_options).await {
            Ok(count) => {
                if let Some(count) = count {
                    Ok(count.count)
                } else {
                    Ok(0)
                }
            }
            Err(_) => Ok(0),
        }
    }

    pub async fn get_list(req: ListParams) -> AppResult<Vec<Stock>> {
        let mut limit: u32 = 15;
        let mut offset: u32 = 0;
        if let Some(page) = req.page {
            limit = page.get_limit().unwrap_or(0);
            offset = page.get_offset();
        }
        let mut where_options = Vec::new();
        if let Some(params) = req.params {
            where_options = params;
        }
        let res = StockBmc::get_list(offset, limit, req.options, where_options).await?;
        Ok(res)
    }

    pub async fn get_by_query(params: Vec<WhereOptions>) -> AppResult<Stock> {
        match StockBmc::get_by_query(params).await {
            Ok(res) => {
                if let Some(res) = res {
                    Ok(res)
                } else {
                    Err(anyhow!("Stock not found.").into())
                }
            }
            Err(e) => Err(e),
        }
    }

    pub async fn get_by_id(id: String) -> AppResult<Stock> {
        match StockBmc::get_by_id(id).await {
            Ok(res) => {
                if let Some(res) = res {
                    Ok(res)
                } else {
                    Err(anyhow!("Stock not found.").into())
                }
            }
            Err(e) => Err(e),
        }
    }

    pub async fn create(req: StockCreate) -> AppResult<String> {
        StockBmc::create(req).await?;
        Ok("Stock created".to_string())
    }

    pub async fn update(req: StockUpdate) -> AppResult<String> {
        StockBmc::update(req).await?;
        Ok("Stock updated".to_string())
    }

    pub async fn delete(id: String) -> AppResult<String> {
        StockBmc::delete(id).await?;
        Ok("Stock deleted".to_string())
    }
}
