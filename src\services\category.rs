use super::role;
use crate::{
    app_writer::AppResult,
    db::{CreateParams, ListOptions, ListParams, UpdateParams, WhereOptions},
    dtos::category::{CategoryCreate, CategoryUpdate},
    entities::category::{Category, CategoryBmc},
};
use anyhow::anyhow;

pub struct CategoryService;
impl CategoryService {
    pub async fn get_total(req: Option<Vec<WhereOptions>>) -> AppResult<u32> {
        let mut where_options = Vec::new();
        if let Some(params) = req {
            where_options = params;
        }
        match CategoryBmc::get_total(where_options).await {
            Ok(count) => {
                if let Some(count) = count {
                    Ok(count.count)
                } else {
                    Ok(0)
                }
            }
            Err(_) => return Ok(0),
        }
    }

    pub async fn get_list(req: ListParams) -> AppResult<Vec<Category>> {
        let mut limit: u32 = 15;
        let mut offset: u32 = 0;
        if let Some(page) = req.page {
            limit = page.get_limit().unwrap_or(0);
            offset = page.get_offset();
        }
        let mut where_options = Vec::new();
        if let Some(params) = req.params {
            where_options = params;
        }
        let res = CategoryBmc::get_list(offset, limit, req.options, where_options).await?;
        Ok(res)
    }

    pub async fn get_by_query(params: Vec<WhereOptions>) -> AppResult<Option<Category>> {
        let res = CategoryBmc::get_by_query(params).await?;
        Ok(res)
    }

    pub async fn get_by_id(id: String) -> AppResult<Option<Category>> {
        let res = CategoryBmc::get_by_id(&id).await?;
        Ok(res)
    }

    pub async fn create(req: CreateParams<CategoryCreate>) -> AppResult<String> {
        CategoryBmc::create(req.data).await?;
        Ok("Category created".to_string())
    }

    pub async fn update(req: UpdateParams<CategoryUpdate>) -> AppResult<String> {
        CategoryBmc::update(req.data).await?;
        Ok("Category updated".to_string())
    }

    pub async fn delete(id: String) -> AppResult<String> {
        CategoryBmc::delete(id).await?;
        Ok("Category deleted".to_string())
    }
}
