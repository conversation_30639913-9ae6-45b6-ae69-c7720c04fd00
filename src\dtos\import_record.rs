use crate::db::{Castable, Creatable, Patchable};
use crate::utils::date::{default_now, default_zero};
use salvo::prelude::{Extractible, ToSchema};
use serde::{Deserialize, Serialize};
use validator::Validate;

#[derive(Default, Clone, Deserialize, Serialize, Debug, Validate, ToSchema)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct ImportRecordCreate {
    pub serial: Option<String>,
    pub contract_id: String, // 项目ID
    pub contract_name: Option<String>,
    pub source: String,     // 获取数据的来源
    pub file_type: Option<String>,
    pub save_dir: String,
    pub file_name: String,
    pub file_link: String,
    // 导入总数量
    #[serde(default = "default_zero")]
    pub total_count: i64,
    // 导入的新条目
    #[serde(default = "default_zero")]
    pub new_count: i64,
    // 导入的更新条目
    #[serde(default = "default_zero")]
    pub update_count: i64,
    // 导入的无变化条目
    #[serde(default = "default_zero")]
    pub no_change_count: i64,
    // 导入成功条目
    #[serde(default = "default_zero")]
    pub success_count: i64,
    // 导入失败条目
    #[serde(default = "default_zero")]
    pub fail_count: i64,
    pub status: String,
    #[serde(default = "default_now")]
    pub created_at: i64,
    #[serde(default = "default_now")]
    pub updated_at: i64,
}

impl Creatable for ImportRecordCreate {}

#[derive(Default, Clone, Debug, Deserialize, Serialize, Extractible, ToSchema)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct ImportRecordUpdate {
    #[salvo(extract(source(from = "param")))]
    pub id: String,
    pub serial: Option<String>,
    pub contract_id: String, // 项目ID
    pub contract_name: Option<String>,
    pub source: String,     // 获取数据的来源
    pub file_type: Option<String>,
    pub save_dir: String,
    pub file_name: String,
    pub file_link: String,
    // 导入总数量
    #[serde(default = "default_zero")]
    pub total_count: i64,
    // 导入的新条目
    #[serde(default = "default_zero")]
    pub new_count: i64,
    // 导入的更新条目
    #[serde(default = "default_zero")]
    pub update_count: i64,
    // 导入的无变化条目
    #[serde(default = "default_zero")]
    pub no_change_count: i64,
    // 导入成功条目
    #[serde(default = "default_zero")]
    pub success_count: i64,
    // 导入失败条目
    #[serde(default = "default_zero")]
    pub fail_count: i64,
    pub status: String,
    #[serde(default = "default_now")]
    pub created_at: i64,
    #[serde(default = "default_now")]
    pub updated_at: i64,
}

impl Patchable for ImportRecordUpdate {}

#[derive(Debug, Deserialize, Serialize, ToSchema, Default, Clone)]
pub struct ImportRecordResponse {
    pub id: String,
    pub serial: Option<String>,
    pub contract_id: String, // 项目ID
    pub contract_name: Option<String>,
    pub source: String,     // 获取数据的来源
    pub file_type: Option<String>,
    pub save_dir: String,
    pub file_name: String,
    pub file_link: String,
    // 导入总数量
    pub total_count: i64,
    // 导入的新条目
    pub new_count: i64,
    // 导入的更新条目
    pub update_count: i64,
    // 导入的无变化条目
    pub no_change_count: i64,
    // 导入成功条目
    pub success_count: i64,
    // 导入失败条目
    pub fail_count: i64,
    pub status: String,
    pub created_at: i64,
    pub updated_at: i64,
}

impl Castable for ImportRecordResponse {}
