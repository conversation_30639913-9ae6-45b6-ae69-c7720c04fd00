use crate::{
    app_writer::{App<PERSON><PERSON><PERSON>, AppWriter},
    db::{CreateParams, ListParams, ListResponse, Page, UpdateParams},
    dtos::financial_contract::{
        FinancialContractCreate, FinancialContractResponse, FinancialContractUpdate,
    },
    services::financial_contract::FinancialContractService,
};

use salvo::{
    oapi::{
        endpoint,
        extract::{JsonBody, PathParam},
    },
    Depot,
};
use salvo::{Router, Writer};

pub fn router() -> Router {
    Router::with_path("financial_contract")
        .post(create_financial_contract)
        .put(update_financial_contract)
        .push(
            Router::with_path("<id>")
                .get(get_financial_contract_by_id)
                .delete(delete_financial_contract),
        )
        .push(Router::with_path("list").post(get_financial_contract_list))
}

#[endpoint(tags("financial_contract"))]
async fn get_financial_contract_list(
    req: JsonBody<ListParams>,
) -> AppWriter<ListResponse<FinancialContractResponse>> {
    let page_params = req.0.page.clone();
    let mut page = Page::default();
    if page_params.is_some() {
        page = page_params.unwrap();
    };
    let list = FinancialContractService::get_list(req.0.clone())
        .await
        .unwrap();
    let mut data: Vec<FinancialContractResponse> = Vec::new();
    for item in list {
        let tmp = item.response();
        data.push(tmp);
    }
    let total = FinancialContractService::get_total(req.0.params)
        .await
        .unwrap();
    let res = ListResponse {
        data,
        total,
        page: page.page,
        size: page.limit,
    };
    AppWriter(Ok(res))
}

#[endpoint(tags("financial_contract"), parameters(("id", description = "financial_contract id for params")))]
async fn get_financial_contract_by_id(
    id: PathParam<String>,
) -> AppWriter<FinancialContractResponse> {
    match FinancialContractService::get_by_id(id.0).await {
        Ok(financial_contract) => {
            let res = financial_contract.response();
            AppWriter(Ok(res))
        }
        Err(e) => AppWriter(Err(e)),
    }
}

#[endpoint(tags("financial_contract"))]
async fn create_financial_contract(
    mut req: JsonBody<FinancialContractCreate>,
    depot: &mut Depot,
) -> AppWriter<String> {
    if req.0.created_by.is_none() {
        let user_id = depot.get::<String>("current_user").unwrap();
        req.0.created_by = Some(user_id.clone())
    };
    let result = FinancialContractService::create(CreateParams { data: req.0 }).await;
    AppWriter(result)
}

#[endpoint(tags("financial_contract"))]
async fn update_financial_contract(
    req: JsonBody<FinancialContractUpdate>,
) -> AppResult<AppWriter<String>> {
    let result = FinancialContractService::update(UpdateParams { data: req.0 }).await;
    Ok(AppWriter(result))
}

#[endpoint(tags("financial_contract"), parameters(("id", description = "user id")))]
async fn delete_financial_contract(id: PathParam<String>) -> AppWriter<String> {
    let result = FinancialContractService::delete(id.0).await;
    AppWriter(result)
}
