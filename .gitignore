# Generated by Cargo
# will have compiled files and executables
debug/
target/
logs/

# Remove Cargo.lock from gitignore if creating an executable, leave it for libraries
# More information here https://doc.rust-lang.org/cargo/guide/cargo-toml-vs-cargo-lock.html
Cargo.lock

# These are backup files generated by rustfmt
**/*.rs.bk


# Added by cargo

/target
/tmp
/migration/target
/adminPanel/node_modules