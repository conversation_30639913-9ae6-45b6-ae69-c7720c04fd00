use crate::db::{Castable, Creatable, Patchable};
use crate::utils::date::default_now;
use salvo::{macros::Extractible, oapi::ToSchema};
use serde::{Deserialize, Serialize};

#[derive(Default, Debug, Deserialize, Serialize, Extractible, ToSchema)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct WarehouseCreate {
    pub name: String,
    pub serial: String,
    pub company: Option<String>,
    pub contacts_person: Option<String>,
    pub contacts_phone: Option<String>,
    pub contact_address: Option<String>,
    pub location: Option<String>,
    pub remark: Option<String>,
    #[serde(default = "default_now")]
    pub created_at: i64,
    #[serde(default = "default_now")]
    pub updated_at: i64,
}

impl Creatable for WarehouseCreate {}

#[derive(Default, Debug, Deserialize, Serialize, Extractible, ToSchema)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct WarehouseUpdate {
    pub id: String,
    pub name: String,
    pub serial: String,
    pub company: Option<String>,
    pub contacts_person: Option<String>,
    pub contacts_phone: Option<String>,
    pub contact_address: Option<String>,
    pub location: Option<String>,
    pub remark: Option<String>,
    #[serde(default = "default_now")]
    pub created_at: i64,
    #[serde(default = "default_now")]
    pub updated_at: i64,
}

impl Patchable for WarehouseUpdate {}

#[derive(Default, Debug, Deserialize, Serialize, Extractible, ToSchema)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct WarehouseResponse {
    pub id: String,
    pub name: String,
    pub serial: String,
    pub company: Option<String>,
    pub contacts_person: Option<String>,
    pub contacts_phone: Option<String>,
    pub contact_address: Option<String>,
    pub location: Option<String>,
    pub remark: Option<String>,
    pub created_at: i64,
    pub updated_at: i64,
}
impl Castable for WarehouseResponse {}
