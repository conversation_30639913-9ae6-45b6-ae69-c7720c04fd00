use crate::utils::date::default_now;
use salvo::oapi::ToSchema;
use serde::{Deserialize, Serialize};
use validator::Validate;

/// 定时任务创建DTO
#[derive(Default, Debug, Serialize, Deserialize, Validate, ToSchema)]
pub struct CronJobCreate {
    /// 任务名称
    #[validate(length(min = 1, max = 250))]
    pub name: String,
    // 归属项目的任务
    pub contract_id: String,
    /// Cron表达式
    #[validate(length(min = 1, max = 500))]
    pub cron_expr: String,
    /// 任务类型：1-HTTP请求, 2-脚本执行, 3-API函数
    #[validate(range(min = 1, max = 9))]
    pub job_type: i32,
    /// 任务参数（JSON格式）
    /// HTTP请求类型：{"method": "GET", "url": "http://example.com", "headers": {}, "body": {}}
    /// 脚本执行类型：{"script": "echo hello", "args": []}
    /// API函数类型：{"api_name": "get_user_info", "args": []}
    pub job_params: String,
    /// 任务描述
    #[validate(length(max = 200))]
    pub description: Option<String>,
    /// 是否启用：0-禁用，1-启用
    pub enabled: i32,
    pub job_status: String,
    pub job_uuid: Option<String>,
    #[serde(default = "default_now")]
    pub created_at: i64,
    #[serde(default = "default_now")]
    pub updated_at: i64,
}

/// 定时任务更新DTO
#[derive(Default, Debug, Serialize, Deserialize, Validate, ToSchema)]
pub struct CronJobUpdate {
    /// 任务ID
    pub id: String,
    // 归属项目的任务
    pub contract_id: Option<String>,
    /// 任务名称
    #[validate(length(min = 1, max = 250))]
    pub name: Option<String>,
    /// Cron表达式
    #[validate(length(min = 1, max = 500))]
    pub cron_expr: Option<String>,
    /// 任务类型：1-HTTP请求, 2-脚本执行, 3-API函数
    #[validate(range(min = 1, max = 9))]
    pub job_type: Option<i32>,
    /// 任务参数（JSON格式）
    pub job_params: Option<String>,
    /// 任务描述
    #[validate(length(max = 200))]
    pub description: Option<String>,
    /// 是否启用：0-禁用，1-启用
    pub enabled: Option<i32>,
    pub job_status: String,
    pub job_uuid: Option<String>,
    #[serde(default = "default_now")]
    pub created_at: i64,
    #[serde(default = "default_now")]
    pub updated_at: i64,
}

/// 定时任务响应DTO
#[derive(Default, Debug, Serialize, Deserialize)]
pub struct CronJobResponse {
    /// 任务ID
    pub id: String,
    // 归属项目的任务
    pub contract_id: String,
    /// 任务名称
    pub name: Option<String>,
    /// Cron表达式
    pub cron_expr: Option<String>,
    /// 任务类型：1-HTTP请求, 2-脚本执行
    pub job_type: Option<i32>,
    /// 任务参数（JSON格式）
    pub job_params: Option<String>,
    /// 任务描述
    pub description: Option<String>,
    /// 是否启用：0-禁用，1-启用
    pub enabled: Option<i32>,
    pub job_status: Option<String>,
    pub job_uuid: Option<String>,
    /// 创建时间
    pub created_at: String,
    /// 更新时间
    pub updated_at: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]

pub struct JobParams {
    pub builder_id: String,
    pub platform: String,
    pub contract_id: String,
}
