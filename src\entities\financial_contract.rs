use std::str::FromStr;

use crate::{
    app_writer::AppResult,
    db::{Castable, CountRecord, Creatable, Database, ListOptions, Patchable, WhereOptions},
    dtos::financial_contract::{
        FinancialContractCreate, FinancialContractResponse, FinancialContractUpdate,
    },
    utils::rand_utils::random_uppercase_serial,
};
use chrono::Local;
use rust_decimal::prelude::*;
use serde::{Deserialize, Serialize};
use surrealdb::RecordId;
use anyhow::anyhow;

#[derive(Debug, Default, Serialize, Deserialize, Clone)]
pub enum CalcPeriod {
    #[default]
    DAY,
    MONTH,
    YEAR,
    QUARTER,
    ONCE
}

impl CalcPeriod {
    /// 获取计算周期的中文显示名称
    pub fn display_name(&self) -> &'static str {
        match self {
            CalcPeriod::DAY => "按天",
            CalcPeriod::MONTH => "按月",
            CalcPeriod::YEAR => "按年",
            CalcPeriod::QUARTER => "按季度",
            CalcPeriod::ONCE => "单次",
        }
    }

    /// 从字符串解析计算周期
    pub fn from_str(s: &str) -> Option<Self> {
        match s.to_uppercase().as_str() {
            "DAY" => Some(CalcPeriod::DAY),
            "MONTH" => Some(CalcPeriod::MONTH),
            "YEAR" => Some(CalcPeriod::YEAR),
            "QUARTER" => Some(CalcPeriod::QUARTER),
            "ONCE" => Some(CalcPeriod::ONCE),
            _ => None,
        }
    }

    /// 获取所有可用的计算周期选项
    pub fn all_options() -> Vec<(CalcPeriod, &'static str)> {
        vec![
            (CalcPeriod::DAY, "按天"),
            (CalcPeriod::MONTH, "按月"),
            (CalcPeriod::YEAR, "按年"),
            (CalcPeriod::QUARTER, "按季度"),
            (CalcPeriod::ONCE, "单次"),
        ]
    }

}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct FinancialContract {
    pub id: Option<RecordId>,
    pub name: String,                  // 项目名称
    pub serial: String,                // 合同编号
    pub begin_time: Option<String>,    // 生效日期
    pub end_time: Option<String>,      // 失效时间
    pub applier: Option<String>,       // 申请人
    pub funder: Option<String>,        // 资金方
    pub scf_company: Option<String>,   // 运营商
    pub contract_type: Option<String>, // 合同类型
    pub status: String,                // 状态
    // pub service_fee: Decimal,          // 服务费
    pub application_quota: Decimal,    // 合同金额(授信额度)
    pub confirm_quota: Decimal,        // 确认额度
    pub used_quota: Decimal,           // 已使用额度
    pub is_delete: bool,
    pub desc: Option<String>, // 描述
    pub created_by: Option<RecordId>,
    pub sign_time: Option<String>,           // 签订时间
    pub supplier_id: Option<RecordId>,       // 供应商
    pub supplier_name: Option<String>,       // 供应商名称
    pub bid_winner_id: Option<RecordId>,     // 中标方ID
    pub bid_winner_name: Option<String>,     // 中标方名称
    pub bid_owner_id: Option<RecordId>,      // 招标方/终端
    pub bid_owner_name: Option<String>,      // 招标方/终端
    pub warehouse_id: Option<RecordId>,      // 物流/仓储方
    pub warehouse_name: Option<String>,      // 物流/仓储方
    pub profit_calc_fee: Decimal,                 // 利润计算费用
    pub penalty_calc_fee: Decimal,                 // 违约金计算费用
    pub profit_calc_period: Option<CalcPeriod>,         // 计算周期
    pub penalty_calc_period: Option<CalcPeriod>, // 违约金计算方式
    pub product_category: Option<String>,    // 商品目录，多个逗号分隔
    pub created_at: i64,
    pub updated_at: i64,
}

impl Castable for FinancialContract {}
impl Creatable for FinancialContract {}
impl Patchable for FinancialContract {}

impl FinancialContract {
    pub fn response(self) -> FinancialContractResponse {
        let created_by = if self.created_by.is_some() {
            Some(self.created_by.clone().unwrap().to_string())
        } else {
            None
        };
        FinancialContractResponse {
            id: self.id.unwrap().to_string(),
            name: self.name,
            serial: self.serial,
            begin_time: self.begin_time,
            end_time: self.end_time,
            applier: self.applier,
            funder: self.funder,
            scf_company: self.scf_company,
            contract_type: self.contract_type,
            status: self.status,
            application_quota: self.application_quota,
            confirm_quota: self.confirm_quota,
            used_quota: self.used_quota,
            is_delete: self.is_delete,
            desc: self.desc,
            created_by: created_by,
            sign_time: self.sign_time,
            supplier_id: self.supplier_id.map(|id| id.to_string()),
            supplier_name: self.supplier_name,
            bid_winner_id: self.bid_winner_id.map(|id| id.to_string()),
            bid_winner_name: self.bid_winner_name,
            bid_owner_id: self.bid_owner_id.map(|id| id.to_string()),
            bid_owner_name: self.bid_owner_name,
            warehouse_id: self.warehouse_id.map(|id| id.to_string()),
            warehouse_name: self.warehouse_name,
            profit_calc_fee: self.profit_calc_fee,
            penalty_calc_fee: self.penalty_calc_fee,
            profit_calc_period: self.profit_calc_period.as_ref().map(|period| period.display_name().to_string()),
            penalty_calc_period: self.penalty_calc_period.as_ref().map(|period| period.display_name().to_string()),
            product_category: self
                .product_category
                .unwrap_or_default()
                .split(",")
                .filter(|s| !s.is_empty())
                .map(|s| s.to_string())
                .collect(),
            created_at: self.created_at,
            updated_at: self.updated_at,
        }
    }

    pub async fn create(financial_contract: FinancialContractCreate) -> FinancialContract {
        let time_now = Local::now().timestamp_millis();
        let created_by = if financial_contract.created_by.is_some() {
            Some(RecordId::from_str(&financial_contract.created_by.unwrap()).unwrap())
        } else {
            None
        };

        // 如果传入的 serial 为 None，则循环生成 serial 直到找到唯一的
        let serial = if let Some(provided_serial) = financial_contract.serial {
            provided_serial
        } else {
            loop {
                let serial = random_uppercase_serial(Some("FC".to_string()), 6);
                let exists = FinancialContractBmc::get_by_query(vec![WhereOptions::new(
                    "serial".to_string(),
                    serial.clone(),
                )])
                .await
                .unwrap()
                .is_some();
                if !exists {
                    break serial;
                }
            }
        };

        // 将 Vec<String> 转换为逗号分隔的字符串
        let product_category = if !financial_contract.product_category.is_empty() {
            Some(financial_contract.product_category.join(","))
        } else {
            None
        };

        // 处理 RecordId 类型的字段
        let supplier_id = financial_contract
            .supplier_id
            .map(|id| RecordId::from_str(&id).unwrap());
        let bid_winner_id = financial_contract
            .bid_winner_id
            .map(|id| RecordId::from_str(&id).unwrap());
        let bid_owner_id = financial_contract
            .bid_owner_id
            .map(|id| RecordId::from_str(&id).unwrap());
        let warehouse_id = financial_contract
            .warehouse_id
            .map(|id| RecordId::from_str(&id).unwrap());

        FinancialContract {
            id: None,
            serial,
            name: financial_contract.name,
            begin_time: financial_contract.begin_time,
            end_time: financial_contract.end_time,
            applier: financial_contract.applier,
            funder: financial_contract.funder,
            scf_company: financial_contract.scf_company,
            contract_type: financial_contract.contract_type,
            status: financial_contract.status,
            application_quota: financial_contract.application_quota,
            confirm_quota: financial_contract.confirm_quota,
            used_quota: financial_contract.used_quota,
            is_delete: false,
            desc: financial_contract.desc,
            created_by,
            sign_time: financial_contract.sign_time,
            supplier_id,
            supplier_name: financial_contract.supplier_name,
            bid_winner_id,
            bid_winner_name: financial_contract.bid_winner_name,
            bid_owner_id,
            bid_owner_name: financial_contract.bid_owner_name,
            warehouse_id,
            warehouse_name: financial_contract.warehouse_name,
            profit_calc_fee: financial_contract.profit_calc_fee,
            penalty_calc_fee: financial_contract.penalty_calc_fee,
            profit_calc_period: financial_contract.profit_calc_period.and_then(|period| CalcPeriod::from_str(&period)),
            penalty_calc_period: financial_contract.penalty_calc_period.and_then(|period| CalcPeriod::from_str(&period)),
            product_category,
            created_at: time_now,
            updated_at: time_now,
        }
    }

     /// 更新金融合同
    /// # 参数
    /// * `new` - 新的金融合同更新数据
    /// * `old` - 原有的金融合同数据
    pub fn update(new: FinancialContractUpdate, old: FinancialContract) -> FinancialContract {
        let time_now = Local::now().timestamp_millis();
        let created_by = new.created_by.map(|id| RecordId::from_str(&id).unwrap());

        // 将 Vec<String> 转换为逗号分隔的字符串
        let product_category = if !new.product_category.is_empty() {
            Some(new.product_category.join(","))
        } else {
            None
        };

        // 处理 RecordId 类型的字段
        let supplier_id = new.supplier_id.map(|id| RecordId::from_str(&id).unwrap());
        let bid_winner_id = new.bid_winner_id.map(|id| RecordId::from_str(&id).unwrap());
        let bid_owner_id = new.bid_owner_id.map(|id| RecordId::from_str(&id).unwrap());
        let warehouse_id = new.warehouse_id.map(|id| RecordId::from_str(&id).unwrap());

        FinancialContract {
            id: old.id.clone(),
            serial: new.serial.unwrap_or(old.serial),
            name: new.name,
            begin_time: new.begin_time.or(old.begin_time),
            end_time: new.end_time.or(old.end_time),
            applier: new.applier.or(old.applier),
            funder: new.funder.or(old.funder),
            scf_company: new.scf_company.or(old.scf_company),
            contract_type: new.contract_type.or(old.contract_type),
            status: new.status.unwrap_or(old.status),
            application_quota: new.application_quota.unwrap_or(old.application_quota),
            confirm_quota: new.confirm_quota.unwrap_or(old.confirm_quota),
            used_quota: new.used_quota.unwrap_or(old.used_quota),
            is_delete: new.is_delete,
            desc: new.desc.or(old.desc),
            created_by: created_by.or(old.created_by),
            sign_time: new.sign_time.or(old.sign_time),
            supplier_id: supplier_id.or(old.supplier_id),
            supplier_name: new.supplier_name.or(old.supplier_name),
            bid_winner_id: bid_winner_id.or(old.bid_winner_id),
            bid_winner_name: new.bid_winner_name.or(old.bid_winner_name),
            bid_owner_id: bid_owner_id.or(old.bid_owner_id),
            bid_owner_name: new.bid_owner_name.or(old.bid_owner_name),
            warehouse_id: warehouse_id.or(old.warehouse_id),
            warehouse_name: new.warehouse_name.or(old.warehouse_name),
            profit_calc_fee: new.profit_calc_fee.unwrap_or(old.profit_calc_fee),
            penalty_calc_fee: new.penalty_calc_fee.unwrap_or(old.penalty_calc_fee),
            profit_calc_period: new.profit_calc_period.and_then(|period| CalcPeriod::from_str(&period)).or(old.profit_calc_period),
            penalty_calc_period: new.penalty_calc_period.and_then(|period| CalcPeriod::from_str(&period)).or(old.penalty_calc_period),
            product_category: product_category.or(old.product_category),
            created_at: old.created_at,
            updated_at: time_now,
        }
    }
}

pub struct FinancialContractBmc;

impl FinancialContractBmc {
    const ENTITY: &'static str = "financial_contract";

    pub async fn get_total(params: Vec<WhereOptions>) -> AppResult<Option<CountRecord>> {
        Database::exec_query_count(Self::ENTITY, params).await
    }

    pub async fn get_list(
        page: u32,
        limit: u32,
        options: Option<ListOptions>,
        params: Vec<WhereOptions>,
    ) -> AppResult<Vec<FinancialContract>> {
        Database::exec_query_list(Self::ENTITY, page, limit, options, params).await
    }

    pub async fn get_by_query(params: Vec<WhereOptions>) -> AppResult<Option<FinancialContract>> {
        Database::exec_get_by_query(Self::ENTITY, params).await
    }

    pub async fn get_by_id(id: &str) -> AppResult<Option<FinancialContract>> {
        Database::exec_get_by_id(Self::ENTITY, id).await
    }

    pub async fn create(req: FinancialContractCreate) -> AppResult<String> {
        let obj = FinancialContract::create(req).await; // 修改这里，添加 .await
        Database::exec_create(Self::ENTITY, obj).await
    }

    /// 更新金融合同
    /// # 参数
    /// * `financial_contract` - 包含更新数据的金融合同对象
    /// # 返回
    /// * `AppResult<String>` - 更新成功返回合同ID，失败返回错误
    pub async fn update(financial_contract: FinancialContractUpdate) -> AppResult<String> {
        // 检查合同是否存在
        let check: Option<FinancialContract> =
            Database::exec_get_by_id(Self::ENTITY, &financial_contract.id.clone()).await?;
        if check.is_none() {
            return Err(anyhow!("FinancialContract not found.").into());
        }
        let old = check.unwrap();
        let tid = old.id.clone().unwrap().to_string();
        let final_tid = if tid.contains(':') {
            tid.split(':').nth(1).unwrap_or(&tid).to_string()
        } else {
            tid
        };
        let obj = FinancialContract::update(financial_contract, old);
        Database::exec_update(Self::ENTITY, &final_tid, obj).await
    }

    pub async fn delete(id: String) -> AppResult<String> {
        Database::exec_delete(Self::ENTITY, id).await
    }
}
