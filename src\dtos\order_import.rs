use salvo::oapi::ToSchema;
use serde::{Deserialize, Serialize};

/// Represents the result of an order import operation.
#[derive(Debug, Serialize, Deserialize, Clone, ToSchema, Default)]
pub struct OrderImportResult {
    /// Total number of orders processed from the input.
    pub total_processed: u32,
    /// Number of orders successfully imported.
    pub successfully_imported: u32,
    /// Number of orders that failed to import.
    pub failed_imports: u32,
    /// A list of error messages for orders that failed to import.
    /// Each string could be a detailed message about a specific order's failure.
    pub errors: Vec<String>,
    /// Optional message providing a general summary or specific information about the import.
    pub message: Option<String>,
}

impl OrderImportResult {
    /// Creates a new, empty OrderImportResult.
    pub fn new() -> Self {
        Self::default()
    }
}
