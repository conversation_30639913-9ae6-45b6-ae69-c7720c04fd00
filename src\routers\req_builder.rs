use crate::{
    app_writer::{App<PERSON><PERSON><PERSON>, AppWriter},
    db::{ListParams, ListResponse, Page},
    dtos::req_builder::{ReqBuilderCreate, ReqBuilderResponse, ReqBuilderUpdate},
    services::req_builder::ReqBuilderService,
};
use salvo::oapi::{
    endpoint,
    extract::{JsonBody, PathParam},
};
use salvo::{Router, Writer};

pub fn router() -> Router {
    Router::with_path("req_builder")
        .post(create_req_builder)
        .put(update_req_builder)
        .push(
            Router::with_path("<id>")
                .get(get_req_builder_by_id)
                .delete(delete_req_builder),
        )
        .push(Router::with_path("list").post(get_req_builder_list))
}

#[endpoint(tags("req_builder"))]
async fn get_req_builder_list(
    req: JsonBody<ListParams>,
) -> AppWriter<ListResponse<ReqBuilderResponse>> {
    let page_params = req.0.page.clone();
    let mut page = Page::default();
    if page_params.is_some() {
        page = page_params.unwrap();
    };
    let list = ReqBuilderService::get_list(req.0.clone()).await.unwrap();
    let mut data: Vec<ReqBuilderResponse> = Vec::new();
    for item in list {
        let tmp = item.response();
        data.push(tmp);
    }
    let total = ReqBuilderService::get_total(req.0.params).await.unwrap();
    let res = ListResponse {
        data,
        total,
        page: page.page,
        size: page.limit,
    };

    AppWriter(Ok(res))
}

#[endpoint(tags("req_builder"), parameters(("id", description = "req_builder id for params")))]
async fn get_req_builder_by_id(id: PathParam<String>) -> AppWriter<ReqBuilderResponse> {
    match ReqBuilderService::get_by_id(id.0).await {
        Ok(req_builder) => {
            let res = req_builder.response();
            AppWriter(Ok(res))
        }
        Err(e) => AppWriter(Err(e)),
    }
}

#[endpoint(tags("req_builder"))]
async fn create_req_builder(req: JsonBody<ReqBuilderCreate>) -> AppWriter<String> {
    let result = ReqBuilderService::create(req.0).await;
    AppWriter(result)
}

#[endpoint(tags("req_builder"))]
async fn update_req_builder(req: JsonBody<ReqBuilderUpdate>) -> AppResult<AppWriter<String>> {
    let result = ReqBuilderService::update(req.0).await;
    Ok(AppWriter(result))
}

#[endpoint(tags("req_builder"), parameters(("id", description = "user id")))]
async fn delete_req_builder(id: PathParam<String>) -> AppWriter<String> {
    let result = ReqBuilderService::delete(id.0).await;
    AppWriter(result)
}
