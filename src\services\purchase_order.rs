use anyhow::anyhow;

use crate::{
    app_writer::AppResult,
    db::{ListParams, WhereOptions},
    dtos::purchase_order::{PurchaseOrderCreate, PurchaseOrderUpdate},
    entities::purchase_order::{PurchaseOrder, PurchaseOrderBmc},
};

pub struct PurchaseOrderService;
impl PurchaseOrderService {
    pub async fn get_total(req: Option<Vec<WhereOptions>>) -> AppResult<u32> {
        let mut where_options = Vec::new();
        if let Some(params) = req {
            where_options = params;
        }
        match PurchaseOrderBmc::get_total(where_options).await {
            Ok(count) => {
                if let Some(count) = count {
                    Ok(count.count)
                } else {
                    Ok(0)
                }
            }
            Err(_) => Ok(0),
        }
    }

    pub async fn get_list(req: ListParams) -> AppResult<Vec<PurchaseOrder>> {
        let mut limit: u32 = 15;
        let mut offset: u32 = 0;
        if let Some(page) = req.page {
            limit = page.get_limit().unwrap_or(0);
            offset = page.get_offset();
        }
        let mut where_options = Vec::new();
        if let Some(params) = req.params {
            where_options = params;
        }
        let res = PurchaseOrderBmc::get_list(offset, limit, req.options, where_options).await?;
        Ok(res)
    }

    pub async fn get_by_query(params: Vec<WhereOptions>) -> AppResult<PurchaseOrder> {
        match PurchaseOrderBmc::get_by_query(params).await {
            Ok(res) => {
                if let Some(res) = res {
                    Ok(res)
                } else {
                    Err(anyhow!("PurchaseOrder not found.").into())
                }
            }
            Err(e) => Err(e),
        }
    }

    pub async fn get_by_id(id: String) -> AppResult<PurchaseOrder> {
        match PurchaseOrderBmc::get_by_id(id).await {
            Ok(res) => {
                if let Some(res) = res {
                    Ok(res)
                } else {
                    Err(anyhow!("PurchaseOrder not found.").into())
                }
            }
            Err(e) => Err(e),
        }
    }

    pub async fn create(req: PurchaseOrderCreate) -> AppResult<String> {
        PurchaseOrderBmc::create(req).await?;
        Ok("PurchaseOrder created".to_string())
    }

    pub async fn update(req: PurchaseOrderUpdate) -> AppResult<String> {
        PurchaseOrderBmc::update(req).await?;
        Ok("PurchaseOrder updated".to_string())
    }

    pub async fn delete(id: String) -> AppResult<String> {
        PurchaseOrderBmc::delete(id).await?;
        Ok("PurchaseOrder deleted".to_string())
    }
}
