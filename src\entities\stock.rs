use crate::{
    app_error::AppError,
    app_writer::AppResult,
    db::{Castable, CountRecord, Creatable, Database, ListOptions, Patchable, WhereOptions},
    dtos::stock::{StockCreate, StockResponse, StockUpdate},
};
use anyhow::anyhow;
use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};
use surrealdb::RecordId;

#[derive(Debug, Serialize, Deserialize)]
pub struct Stock {
    pub id: Option<RecordId>,
    pub sku_serial: String,
    pub warehouse: String,
    pub position: String,
    pub company: Option<String>,
    pub quantity: Decimal,
    pub location: Option<Vec<String>>,
    pub remark: Option<String>,
    created_at: i64,
    updated_at: i64,
}

impl Stock {
    pub fn response(self) -> StockResponse {
        StockResponse {
            id: self.id.unwrap().to_string(),
            sku_serial: self.sku_serial,
            warehouse: self.warehouse,
            position: self.position,
            company: self.company,
            quantity: self.quantity,
            location: self.location,
            remark: self.remark,
            created_at: self.created_at,
            updated_at: self.updated_at,
        }
    }

    pub fn create(stock: StockCreate) -> Stock {
        Stock {
            id: None,
            sku_serial: stock.sku_serial,
            warehouse: stock.warehouse,
            position: stock.position,
            company: stock.company,
            quantity: stock.quantity,
            location: stock.location,
            remark: stock.remark,
            created_at: stock.created_at,
            updated_at: stock.updated_at,
        }
    }

    pub fn update(new: StockUpdate, old: Stock) -> Stock {
        Stock {
            id: old.id,
            sku_serial: new.sku_serial,
            warehouse: new.warehouse,
            position: new.position,
            company: new.company,
            quantity: new.quantity,
            location: new.location,
            remark: new.remark,
            created_at: old.created_at,
            updated_at: new.updated_at,
        }
    }
}

impl Creatable for Stock {}
impl Patchable for Stock {}
impl Castable for Stock {}

pub struct StockBmc;

impl StockBmc {
    const ENTITY: &'static str = "stock";

    pub async fn get_total(params: Vec<WhereOptions>) -> AppResult<Option<CountRecord>> {
        Database::exec_query_count(Self::ENTITY, params).await
    }

    pub async fn get_list(
        page: u32,
        limit: u32,
        options: Option<ListOptions>,
        params: Vec<WhereOptions>,
    ) -> AppResult<Vec<Stock>> {
        Database::exec_query_list(Self::ENTITY, page, limit, options, params).await
    }

    pub async fn get_by_id(id: String) -> AppResult<Option<Stock>> {
        Database::exec_get_by_id(Self::ENTITY, &id).await
    }

    pub async fn get_by_query(params: Vec<WhereOptions>) -> AppResult<Option<Stock>> {
        Database::exec_get_by_query(Self::ENTITY, params).await
    }

    pub async fn create(stock: StockCreate) -> AppResult<String> {
        Database::exec_create(Self::ENTITY, stock).await
    }

    pub async fn update(stock: StockUpdate) -> AppResult<String> {
        let check: Option<Stock> =
            Database::exec_get_by_id(Self::ENTITY, &stock.id.clone()).await?;
        if check.is_none() {
            return Err(AppError::AnyHow(anyhow!("Stock not found.")));
        }
        let old = check.unwrap();
        let tid = old.id.clone().unwrap().to_string();
        let final_tid = if tid.contains(':') {
            tid.split(':').nth(1).unwrap_or(&tid).to_string()
        } else {
            tid
        };
        let obj = Stock::update(stock, old);
        Database::exec_update(Self::ENTITY, &final_tid, obj).await
    }

    pub async fn delete(id: String) -> AppResult<String> {
        Database::exec_delete(Self::ENTITY, id).await
    }
}
