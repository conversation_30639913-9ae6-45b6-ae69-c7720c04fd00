use crate::{
    app_writer::{<PERSON><PERSON><PERSON><PERSON><PERSON>, AppWriter},
    db::{CreateParams, ListParams, ListResponse, Page, RelateParams, UpdateParams, WhereOptions},
    dtos::role::{RoleCreate, RoleResponse, RoleUpdate},
    services::role::RoleService,
};
use anyhow::anyhow;
use salvo::{
    oapi::endpoint,
    oapi::extract::{JsonBody, PathParam},
};
use salvo::{Router, Writer};

pub fn router() -> Router {
    Router::with_path("role")
        .post(create_role)
        .put(update_role)
        .push(
            Router::with_path("<id>")
                .get(get_role_by_id)
                .delete(delete_role),
        )
        .push(Router::with_path("<rolename>").get(get_role_by_name))
        .push(
            Router::with_path("relate")
                .push(Router::with_path("menu").post(relate_to_menu))
                .push(Router::with_path("permission").post(relate_to_permission)),
        )
        .push(Router::with_path("list").post(get_role_list))
}

#[endpoint(tags("role"))]
async fn get_role_list(req: JsonBody<ListParams>) -> AppWriter<ListResponse<RoleResponse>> {
    let page_params = req.0.page.clone();
    let mut page = Page::default();
    if page_params.is_some() {
        page = page_params.unwrap();
    };
    let list = RoleService::get_list(req.0.clone()).await.unwrap();

    let mut data: Vec<RoleResponse> = Vec::new();
    for item in list {
        let tmp = item.response();
        data.push(tmp);
    }
    let total = RoleService::get_total(req.0.params).await.unwrap();
    let res = ListResponse {
        data,
        total,
        page: page.page,
        size: page.limit,
    };

    AppWriter(Ok(res))
}

#[endpoint(tags("role"), parameters(("id", description = "role id")))]
async fn get_role_by_id(id: PathParam<String>) -> AppWriter<RoleResponse> {
    match RoleService::get_by_id(id.0).await {
        Ok(role) => {
            let res = role.response();
            AppWriter(Ok(res))
        }
        Err(e) => AppWriter(Err(e)),
    }
}

#[endpoint(tags("role"), parameters(("rolename", description = "role name")))]
async fn get_role_by_name(rolename: PathParam<String>) -> AppWriter<RoleResponse> {
    let params = vec![WhereOptions {
        var: "name".to_string(),
        val: rolename.0,
    }];
    match RoleService::get_by_query(params).await {
        Ok(role) => {
            if let Some(role) = role {
                let res = role.response();
                AppWriter(Ok(res))
            } else {
                AppWriter(Err(anyhow!("role not found").into()))
            }
        }
        Err(e) => AppWriter(Err(e)),
    }
}

#[endpoint(tags("role"))]
async fn get_menu_list_by_role(
    req: JsonBody<ListParams>,
) -> AppResult<AppWriter<Vec<RoleResponse>>> {
    let list = RoleService::get_list_by_role(req.0, "->has_permission->menu.* AS menu".to_string())
        .await?;
    let mut res: Vec<RoleResponse> = Vec::new();
    for item in list {
        let tmp = item.response();
        res.push(tmp);
    }
    Ok(AppWriter(Ok(res)))
}

#[endpoint(tags("role"))]
async fn relate_to_permission(req: JsonBody<RelateParams>) -> AppWriter<String> {
    let result = RoleService::relate_to_permission(req.0).await;
    AppWriter(result)
}

#[endpoint(tags("role"))]
async fn relate_to_menu(req: JsonBody<RelateParams>) -> AppWriter<String> {
    let result = RoleService::relate_to_menu(req.0).await;
    AppWriter(result)
}

#[endpoint(tags("role"))]
async fn create_role(req: JsonBody<RoleCreate>) -> AppWriter<String> {
    let result = RoleService::create(CreateParams { data: req.0 }).await;
    AppWriter(result)
}

#[endpoint(tags("role"))]
async fn update_role(req: JsonBody<RoleUpdate>) -> AppResult<AppWriter<String>> {
    let result = RoleService::update(UpdateParams { data: req.0 }).await;
    Ok(AppWriter(result))
}

#[endpoint(tags("role"), parameters(("id", description = "role id")))]
async fn delete_role(id: PathParam<String>) -> AppWriter<String> {
    let result = RoleService::delete(id.0).await;
    AppWriter(result)
}
