use crate::{
    app_writer::{App<PERSON><PERSON><PERSON>, AppWriter},
    db::{ListParams, ListResponse, WhereOptions},
    dtos::purchase_order::{PurchaseOrderCreate, PurchaseOrderResponse, PurchaseOrderUpdate},
    services::purchase_order::PurchaseOrderService,
};
use salvo::{
    oapi::{
        endpoint,
        extract::{JsonBody, PathParam},
    },
    Depot, Request,
};
use salvo::{Router, Writer};

pub fn router() -> Router {
    Router::with_path("purchase_order")
        .post(create_purchase_order)
        .put(update_purchase_order)
        .push(
            Router::with_path("<id>")
                .get(get_purchase_order_by_id)
                .delete(delete_purchase_order),
        )
        .push(Router::with_path("list").post(get_purchase_order_list))
}

#[endpoint(tags("purchase_order"))]
async fn get_purchase_order_list(
    req: JsonBody<ListParams>,
) -> AppWriter<ListResponse<PurchaseOrderResponse>> {
    let list = PurchaseOrderService::get_list(req.0.clone()).await.unwrap();

    let mut data: Vec<PurchaseOrderResponse> = Vec::new();
    for item in list {
        let tmp = item.response().await;
        data.push(tmp);
    }
    let page = req.0.page.clone().unwrap();
    let total = PurchaseOrderService::get_total(req.params.clone())
        .await
        .unwrap();
    let res = ListResponse {
        data,
        total,
        page: page.page,
        size: page.limit,
    };

    AppWriter(Ok(res))
}

#[endpoint(tags("purchase_order"), parameters(("id", description = "purchase_order id for params")))]
async fn get_purchase_order_by_id(id: PathParam<String>) -> AppWriter<PurchaseOrderResponse> {
    match PurchaseOrderService::get_by_id(id.0).await {
        Ok(purchase_order) => {
            let res = purchase_order.response().await;
            AppWriter(Ok(res))
        }
        Err(e) => AppWriter(Err(e)),
    }
}

#[endpoint(tags("purchase_order"))]
async fn create_purchase_order(req: JsonBody<PurchaseOrderCreate>) -> AppWriter<String> {
    let result = PurchaseOrderService::create(req.0).await;
    AppWriter(result)
}

#[endpoint(tags("purchase_order"))]
async fn update_purchase_order(req: JsonBody<PurchaseOrderUpdate>) -> AppResult<AppWriter<String>> {
    let result = PurchaseOrderService::update(req.0).await;
    Ok(AppWriter(result))
}

#[endpoint(tags("purchase_order"), parameters(("id", description = "user id")))]
async fn delete_purchase_order(id: PathParam<String>) -> AppWriter<String> {
    let result = PurchaseOrderService::delete(id.0).await;
    AppWriter(result)
}
