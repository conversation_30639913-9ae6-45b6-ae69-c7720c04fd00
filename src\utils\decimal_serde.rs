use rust_decimal::Decimal;
use serde::{Deserialize, Deserializer};
use std::str::FromStr;

pub fn deserialize_decimal_from_float_or_string<'de, D>(
    deserializer: D,
) -> Result<Decimal, D::Error>
where
    D: Deserializer<'de>,
{
    #[derive(Deserialize)]
    #[serde(untagged)]
    enum StringOrFloat {
        String(String),
        Float(f64),
        Int(i64),
    }

    match StringOrFloat::deserialize(deserializer)? {
        StringOrFloat::String(s) => Decimal::from_str(&s).map_err(serde::de::Error::custom),
        StringOrFloat::Float(f) => Decimal::try_from(f).map_err(serde::de::Error::custom),
        StringOrFloat::Int(i) => Ok(Decimal::from(i)),
    }
}
