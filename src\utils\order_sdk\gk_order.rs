use crate::{
    app_error::AppError,
    app_writer::AppResult,
    dtos::{
        req_builder::ReqBuilderUpdate, sales_order::SalesOrderCreate,
        sales_order_info::SalesOrderInfoCreate,
    },
    services::req_builder::ReqBuilderService,
};
use anyhow::anyhow;
use calamine::{open_workbook, Reader, Xlsx};
use chrono::{DateTime, TimeZone, Utc};
use reqwest::Client;
use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};
use serde_json::Value;
use std::{collections::HashMap, path::Path, str::FromStr};

#[derive(Debug, Serialize, Deserialize)]
pub struct GkOrder {
    #[serde(rename = "合同ID")]
    pub contract_id: Option<String>,
    // 订单基本信息
    #[serde(rename = "序号")]
    pub order_index: String,
    #[serde(rename = "包裹单号")]
    pub package_id: String,
    #[serde(rename = "销售单号")]
    pub order_id: String,
    #[serde(rename = "订单状态")]
    pub order_status: String,
    #[serde(rename = "订单创建时间")]
    pub order_time: Option<String>,
    #[serde(rename = "订单推送时间")]
    pub push_time: Option<String>,

    // 商品信息
    #[serde(rename = "商品来源")]
    pub product_source: String,
    #[serde(rename = "数量")]
    pub quantity: String,
    #[serde(rename = "商品名称")]
    pub product_name: String,
    #[serde(rename = "规格")]
    pub product_spec: Option<String>,
    #[serde(rename = "单位")]
    pub unit: Option<String>,
    #[serde(rename = "商品ID")]
    pub product_id: String,
    #[serde(rename = "商品编码")]
    pub product_code: String,
    #[serde(rename = "商品SKU编号")]
    pub sku_id: String,
    #[serde(rename = "SKU编码")]
    pub sku_code: String,
    #[serde(rename = "条码")]
    pub barcode: String,

    // 金额信息
    #[serde(rename = "订单金额")]
    pub order_amount: Option<String>,
    #[serde(rename = "运费")]
    pub shipping_fee: Option<String>,
    #[serde(rename = "销售单价")]
    pub unit_price: Option<String>,
    #[serde(rename = "商品销售金额")]
    pub product_amount: Option<String>,
    #[serde(rename = "供货价")]
    pub supply_price: Option<String>,
    #[serde(rename = "货款金额")]
    pub payment_amount: Option<String>,

    // 收货信息
    #[serde(rename = "收货人")]
    pub receiver: String,
    #[serde(rename = "收货人手机号")]
    pub receiver_phone: String,
    #[serde(rename = "详细收货地址")]
    pub address: String,
    #[serde(rename = "买家备注")]
    pub buyer_remark: Option<String>,

    // 物流信息
    #[serde(rename = "快递公司")]
    pub express_company: String,
    #[serde(rename = "快递单号")]
    pub express_number: String,
    #[serde(rename = "是否退货")]
    pub is_return: String,
    #[serde(rename = "是否换货")]
    pub is_exchange: String,
}

impl GkOrder {
    // 转换为SalesOrderCreate
    pub fn to_sales_order_create(&self) -> SalesOrderCreate {
        SalesOrderCreate {
            contract_id: self.contract_id.clone(),
            status: Some(self.order_status.clone()),
            serial: self.order_id.clone(),
            purchase_time: self.order_time.clone(),
            pay_time: self.push_time.clone(),
            pay_type: None,
            pay_info: None,
            customer: Some(self.receiver.clone()),
            receive_phone: Some(self.receiver_phone.clone()),
            customer_phone: Some(self.receiver_phone.clone()),
            address: Some(self.address.clone()),
            express_type: None,
            express_company: Some(self.express_company.clone()),
            express_order: Some(self.express_number.clone()),
            platform_name: Some("广垦".to_string()),
            platform_serial: None,
            platform_order_serial: Some(self.order_id.clone()),
            platform_fee_total: Decimal::new(0, 2),
            amount: Decimal::from_str(
                &self.order_amount.clone().unwrap_or_else(|| "0".to_string()),
            )
            .unwrap_or_else(|_| Decimal::new(0, 2)),
            express_fee: Decimal::from_str(
                &self.shipping_fee.clone().unwrap_or_else(|| "0".to_string()),
            )
            .unwrap_or_else(|_| Decimal::new(0, 2)),
            total_payment: Decimal::from_str(
                &self
                    .payment_amount
                    .clone()
                    .unwrap_or_else(|| "0".to_string()),
            )
            .unwrap_or_else(|_| Decimal::new(0, 2)),
            creator_id: None,
            updater_id: None,
            delivery_time: None,
            sign_time: None,
            complete_time: None,
            ..Default::default()
        }
    }
    // 转换为SalesOrderInfoCreate
    pub fn to_sales_order_info_create(&self) -> SalesOrderInfoCreate {
        SalesOrderInfoCreate {
            order_serial: self.order_id.clone(),
            product_serial: self.sku_id.clone(),
            product_name: Some(self.product_name.clone()),
            product_model: self.product_spec.clone(),
            product_type: None,
            sales_price: Decimal::from_str(
                &self.unit_price.clone().unwrap_or_else(|| "0".to_string()),
            )
            .unwrap_or_else(|_| Decimal::new(0, 2)),
            cost_price: Decimal::from_str(
                &self.supply_price.clone().unwrap_or_else(|| "0".to_string()),
            )
            .unwrap_or_else(|_| Decimal::new(0, 2)),
            platform_fee: Decimal::default(),
            discount: Decimal::default(),
            quantity: Decimal::from_str(&self.quantity).unwrap_or_else(|_| Decimal::new(0, 2)),
            total_sales_price: Decimal::from_str(
                &self
                    .product_amount
                    .clone()
                    .unwrap_or_else(|| "0".to_string()),
            )
            .unwrap_or_else(|_| Decimal::new(0, 2)),
            total_cost_price: Decimal::default(),
            express_company: Some(self.express_company.clone()),
            express_order: Some(self.express_number.clone()),
            system_remark: self.buyer_remark.clone(),
            ..Default::default()
        }
    }
}

pub async fn import_orders(file_path: &Path, sheet: Option<String>, contract_id: Option<String>) -> AppResult<Vec<GkOrder>> {
    let mut workbook: Xlsx<_> = match open_workbook(file_path) {
        Ok(wb) => wb,
        Err(e) => return Err(AppError::XlsxError(e.to_string())),
    };
    let sheet = match sheet {
        Some(sheet) => sheet,
        None => "sheet1".to_string(),
    };
    let range = match workbook.worksheet_range(&sheet) {
        Ok(range) => range,
        Err(e) => return Err(AppError::XlsxError(e.to_string())),
    };

    let mut orders = Vec::new();

    for row in range.rows().skip(1) {
        // 跳过标题行
        let order = GkOrder {
            contract_id: contract_id.clone(),
            order_index: row[0].to_string(),
            package_id: row[1].to_string(),
            order_id: row[2].to_string(),
            order_status: row[3].to_string(),
            order_time: {
                let val = row[4].to_string();
                if val.is_empty() {
                    None
                } else {
                    Some(val)
                }
            },
            push_time: {
                let val = row[5].to_string();
                if val.is_empty() {
                    None
                } else {
                    Some(val)
                }
            },
            product_source: row[6].to_string(),
            quantity: row[7].to_string(),
            product_name: row[8].to_string(),
            product_spec: {
                let val = row[9].to_string();
                if val.is_empty() {
                    None
                } else {
                    Some(val)
                }
            },
            unit: {
                let val = row[10].to_string();
                if val.is_empty() {
                    None
                } else {
                    Some(val)
                }
            },
            product_id: row[11].to_string(),
            product_code: row[12].to_string(),
            sku_id: row[13].to_string(),
            sku_code: row[14].to_string(),
            barcode: row[15].to_string(),
            order_amount: {
                let val = row[16].to_string();
                if val.is_empty() {
                    Some("0".to_string())
                } else {
                    Some(val)
                }
            },
            shipping_fee: {
                let val = row[17].to_string();
                if val.is_empty() {
                    Some("0".to_string())
                } else {
                    Some(val)
                }
            },
            unit_price: {
                let val = row[18].to_string();
                if val.is_empty() {
                    Some("0".to_string())
                } else {
                    Some(val)
                }
            },
            product_amount: {
                let val = row[19].to_string();
                if val.is_empty() {
                    Some("0".to_string())
                } else {
                    Some(val)
                }
            },
            supply_price: {
                let val = row[20].to_string();
                if val.is_empty() {
                    Some("0".to_string())
                } else {
                    Some(val)
                }
            },
            payment_amount: {
                let val = row[21].to_string();
                if val.is_empty() {
                    Some("0".to_string())
                } else {
                    Some(val)
                }
            },
            receiver: row[22].to_string(),
            receiver_phone: row[23].to_string(),
            address: row[24].to_string(),
            buyer_remark: {
                let val = row[25].to_string();
                if val.is_empty() {
                    None
                } else {
                    Some(val)
                }
            },
            express_company: row[26].to_string(),
            express_number: row[27].to_string(),
            is_return: row[28].to_string(),
            is_exchange: row[29].to_string(),
        };
        orders.push(order);
    }

    Ok(orders)
}

pub async fn req_import_orders(builder_id: String, contract_id: String) -> AppResult<Vec<GkOrder>> {
    let builder =
        ReqBuilderService::get_by_id(builder_id.clone()).await?;

    // 构建HTTP客户端
    let client = Client::new();
    let mut request = client.request(
        reqwest::Method::from_bytes(builder.method.as_bytes()).unwrap(),
        &builder.url,
    );

    // 设置请求头
    if let Some(headers_str) = builder.headers {
        let headers: HashMap<String, String> =
            serde_json::from_str(&headers_str).unwrap_or_default();
        for (key, value) in headers {
            request = request.header(&key, value);
        }
    }

    // 设置Bearer Token
    if let Some(token) = builder.bearer_token {
        request = request.bearer_auth(token);
    }

    // 设置超时
    if let Some(timeout) = builder.timeout_secs {
        request = request.timeout(std::time::Duration::from_secs(timeout));
    }

    // 发送请求并获取响应
    let response = match request.send().await {
        Ok(resp) => {
            if resp.status() == reqwest::StatusCode::UNAUTHORIZED {
                // 更新token过期状态
                let update = ReqBuilderUpdate {
                    id: builder_id.clone(),
                    token_expired: Some(true),
                    updated_at: chrono::Local::now().timestamp(),
                    ..Default::default()
                };
                let _ = ReqBuilderService::update(update).await;
                return Err(AppError::AnyHow(anyhow!("Token已过期，请更新token后重试")));
            }
            resp.json::<Value>()
                .await
                .map_err(|e| AppError::JsonParseError(format!("Failed to parse response: {}", e)))?
        }
        Err(e) => return Err(AppError::AnyHow(anyhow!("请求失败: {}", e))),
    };

    // 解析响应数据
    let content = response["data"]["content"]
        .as_array()
        .ok_or_else(|| AppError::AnyHow(anyhow!("Invalid response format")))?;

    let mut orders = Vec::new();

    for order_data in content {
        let order_info = &order_data["orderPackages"][0]["orderInfos"][0];
        let logistic = &order_data["logistic"];

        // 转换时间戳
        let create_time = order_data["createTime"].as_i64().unwrap_or_default();
        let push_time = order_data["pushTime"].as_i64().unwrap_or_default();
        let order_time = if create_time > 0 {
            Some(
                DateTime::<Utc>::from_timestamp_millis(create_time)
                    .unwrap_or_default()
                    .format("%Y-%m-%d %H:%M:%S")
                    .to_string(),
            )
        } else {
            None
        };
        let push_time = if push_time > 0 {
            Some(
                DateTime::<Utc>::from_timestamp_millis(push_time)
                    .unwrap_or_default()
                    .format("%Y-%m-%d %H:%M:%S")
                    .to_string(),
            )
        } else {
            None
        };

        // 构建完整地址
        let address = format!(
            "{}{}{}{}{}",
            logistic["province"].as_str().unwrap_or_default(),
            logistic["city"].as_str().unwrap_or_default(),
            logistic["district"].as_str().unwrap_or_default(),
            logistic["town"].as_str().unwrap_or_default(),
            logistic["address"].as_str().unwrap_or_default()
        );

        let order = GkOrder {
            contract_id: Some(contract_id.clone()),
            order_index: order_data["id"].to_string(),
            package_id: order_data["orderPackages"][0]["no"]
                .as_str()
                .unwrap_or_default()
                .to_string(),
            order_id: order_data["no"].as_str().unwrap_or_default().to_string(),
            order_status: order_data["status"].to_string(),
            order_time,
            push_time,
            product_source: order_data["sourceName"]
                .as_str()
                .unwrap_or_default()
                .to_string(),
            quantity: order_info["num"].to_string(),
            product_name: order_info["goodsName"]
                .as_str()
                .unwrap_or_default()
                .to_string(),
            product_spec: Some(
                order_info["skuSpec"]
                    .as_str()
                    .unwrap_or_default()
                    .to_string(),
            ),
            unit: Some(order_info["unit"].as_str().unwrap_or_default().to_string()),
            product_id: order_info["goodsDetailId"].to_string(),
            product_code: order_info["rowNo"].as_str().unwrap_or_default().to_string(),
            sku_id: order_info["skuId"].to_string(),
            sku_code: order_info["rowNo"].as_str().unwrap_or_default().to_string(),
            barcode: order_info["rowNo"].as_str().unwrap_or_default().to_string(),
            order_amount: Some(order_data["totalRealPrice"].to_string()),
            shipping_fee: Some(order_data["postFee"].to_string()),
            unit_price: Some(order_info["salePrice"].to_string()),
            product_amount: Some(order_info["realPrice"].to_string()),
            supply_price: Some(order_info["costPrice"].to_string()),
            payment_amount: Some(order_data["totalRealPrice"].to_string()),
            receiver: logistic["consignee"]
                .as_str()
                .unwrap_or_default()
                .to_string(),
            receiver_phone: logistic["mobile"].as_str().unwrap_or_default().to_string(),
            address,
            buyer_remark: None,
            express_company: "".to_string(), // 接口中未提供
            express_number: "".to_string(),  // 接口中未提供
            is_return: order_info["isReturned"].to_string(),
            is_exchange: order_info["isExchanged"].to_string(),
        };

        orders.push(order);
    }

    Ok(orders)
}
