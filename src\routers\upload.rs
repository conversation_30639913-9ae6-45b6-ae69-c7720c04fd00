use crate::app_writer::AppWriter;
use crate::services::order_import::{
    platform_importers::{GkOrderImporter, VipOrderImporter},
    OrderImportService,
};
use crate::{
    app_writer::AppResult,
    services::upload::UploadService,
    utils::order_sdk::{gk_order, vip_order},
};
use anyhow::anyhow;
use salvo::{
    oapi::{endpoint, extract::PathParam},
    Depot, Request, Router, Writer,
};

pub fn router() -> Router {
    Router::with_path("upload")
        .post(upload_single)
        .push(Router::with_path("multi").post(upload_multi))
        .push(Router::with_path("temp").post(upload_temp))
        .push(Router::with_path("<id>").put(update_upload))
}

#[derive(Debug)]
enum FileType {
    CONTENT,
    FILE,
    IMAGE,
    SLIDER,
    AVATAR,
    Attachment,
}

impl FileType {
    fn from_str(s: &str) -> Option<FileType> {
        match s.to_uppercase().as_str() {
            "CONTENT" => Some(FileType::CONTENT),
            "FILE" => Some(FileType::FILE),
            "IMAGE" => Some(FileType::IMAGE),
            "SLIDER" => Some(FileType::SLIDER),
            "AVATAR" => Some(FileType::AVATAR),
            "ATTACHMENT" => Some(FileType::Attachment),
            _ => None,
        }
    }

    fn as_str(&self) -> &'static str {
        match self {
            FileType::CONTENT => "content",
            FileType::FILE => "file",
            FileType::IMAGE => "image",
            FileType::SLIDER => "slider",
            FileType::AVATAR => "avatar",
            FileType::Attachment => "attachment",
        }
    }
}

#[endpoint(tags("upload"))]
async fn upload_single(req: &mut Request, depot: &mut Depot) -> AppResult<String> {
    let uid = depot.get::<String>("current_user").unwrap();
    let file = req.form_data().await?.files.get("file").cloned();
    if file.is_none() {
        return Err(anyhow!("文件上传失败").into());
    }
    let file_type = match req.form::<String>("file_type").await {
        Some(t) => match FileType::from_str(&t) {
            Some(file_type) => file_type,
            None => return Err(anyhow!("文件类型不能为空").into()),
        },
        None => return Err(anyhow!("文件类型不能为空").into()),
    };
    let tid = match req.form::<String>("tid").await {
        Some(id) => id,
        None => return Err(anyhow!("关联id不能为空").into()),
    };
    let res = UploadService::create_upload(
        file.unwrap(),
        file_type.as_str().to_string(),
        uid.clone(),
        tid,
        false,
    )
    .await?;
    Ok(res)
}

#[endpoint(tags("upload"))]
async fn upload_multi(req: &mut Request, depot: &mut Depot) -> AppResult<String> {
    let file_list = req.form_data().await?;
    let uid = depot.get::<String>("current_user").unwrap();
    let files = file_list
        .files
        .iter()
        .map(|(_, file)| file.clone())
        .collect::<Vec<_>>();
    let file_type = match req.form::<String>("file_type").await {
        Some(t) => match FileType::from_str(&t) {
            Some(file_type) => file_type,
            None => return Err(anyhow!("文件类型不能为空").into()),
        },
        None => return Err(anyhow!("文件类型不能为空").into()),
    };
    let tid = match req.form::<String>("tid").await {
        Some(id) => id,
        None => return Err(anyhow!("关联id不能为空").into()),
    };
    let res = UploadService::create_upload_multi(
        files,
        file_type.as_str().to_string(),
        uid.clone(),
        tid,
        false,
    )
    .await?;
    Ok(res)
}

// #[endpoint(tags("upload"))]
// async fn delete(id: PathParam<String>) -> AppResult<String> {
//     let target = req.extract::<String>().await?;
//     UploadService::delete(&target).await
// }

#[endpoint(tags("upload"))]
async fn upload_temp(req: &mut Request, depot: &mut Depot) -> AppResult<String> {
    let uid = depot.get::<String>("current_user").unwrap();
    let file = req.form_data().await?.files.get("file").cloned();
    if file.is_none() {
        return Err(anyhow!("文件上传失败").into());
    }
    let file_type = match req.form::<String>("file_type").await {
        Some(t) => match FileType::from_str(&t) {
            Some(file_type) => file_type,
            None => return Err(anyhow!("文件类型不能为空").into()),
        },
        None => return Err(anyhow!("文件类型不能为空").into()),
    };
    let tid = match req.form::<String>("tid").await {
        Some(id) => id,
        None => return Err(anyhow!("关联id不能为空").into()),
    };
    let res = UploadService::create_upload(
        file.unwrap(),
        file_type.as_str().to_string(),
        uid.clone(),
        tid,
        true,
    )
    .await?;
    Ok(res)
}

// 更新上传的文件，如果文件时临时文件，则移动去永久目录
#[endpoint(tags("upload"), parameters(("id", description = "upload id for params")))]
async fn update_upload(id: PathParam<String>, depot: &mut Depot) -> AppResult<String> {
    let uid = depot.get::<String>("current_user").unwrap();
    let tid = id.0;
    let res = UploadService::update_upload(uid.clone(), tid).await?;
    Ok(res)
}

