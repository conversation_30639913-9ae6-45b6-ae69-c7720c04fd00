use crate::app_writer::AppR<PERSON>ult;
use crate::dtos::order_import::OrderImportResult;
use gk_order::GkOrder;
use salvo::http::form::FilePart;

pub mod gk_order;
pub mod vip_order;

/// Defines the contract for order import operations.
/// Implementors of this trait will provide specific logic for importing orders
/// from different sources or formats.
pub trait OrderImportMethod {
    /// Imports orders from a given file part.
    ///
    /// # Arguments
    ///
    /// * `file` - A `FilePart` representing the uploaded file containing order data.
    /// * `target_project_id` - An optional string slice representing the ID of the project
    ///   to which the orders should be imported. This can be used to associate orders
    ///   with a specific project or context.
    ///
    /// # Returns
    ///
    /// An `AppResult` wrapping an `OrderImportResult`. On success, `OrderImportResult`
    /// contains details about the import process, such as the number of successfully
    /// imported orders, failures, and any error messages. On failure, `AppResult`
    /// will contain an error indicating what went wrong during the import process.
    async fn import_orders(
        &self,
        file: FilePart,
        target_project_id: Option<&str>,
    ) -> AppResult<OrderImportResult>;
}

pub enum PlatformName {
    GK(gk_order::GkOrder),
    VIP(vip_order::VipOrder),
}
