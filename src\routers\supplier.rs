use crate::{
    app_writer::{App<PERSON><PERSON><PERSON>, AppWriter},
    db::{ListParams, ListResponse, Page},
    dtos::supplier::{SupplierCreate, SupplierResponse, SupplierUpdate},
    services::supplier::SupplierService,
};
use salvo::oapi::{
    endpoint,
    extract::{JsonBody, PathParam},
};
use salvo::{Router, Writer};

pub fn router() -> Router {
    Router::with_path("supplier")
        .post(create_supplier)
        .put(update_supplier)
        .push(
            Router::with_path("<id>")
                .get(get_supplier_by_id)
                .delete(delete_supplier),
        )
        .push(Router::with_path("list").post(get_supplier_list))
}

#[endpoint(tags("supplier"))]
async fn get_supplier_list(req: JsonBody<ListParams>) -> AppWriter<ListResponse<SupplierResponse>> {
    let page_params = req.0.page.clone();
    let mut page = Page::default();
    if page_params.is_some() {
        page = page_params.unwrap();
    };
    let list = SupplierService::get_list(req.0.clone()).await.unwrap();
    let mut data: Vec<SupplierResponse> = Vec::new();
    for item in list {
        let tmp = item.response();
        data.push(tmp);
    }
    let total = SupplierService::get_total(req.0.params).await.unwrap();
    let res = ListResponse {
        data,
        total,
        page: page.page,
        size: page.limit,
    };
    AppWriter(Ok(res))
}

#[endpoint(tags("supplier"), parameters(("id", description = "supplier id for params")))]
async fn get_supplier_by_id(id: PathParam<String>) -> AppWriter<SupplierResponse> {
    match SupplierService::get_by_id(id.0).await {
        Ok(supplier) => {
            let res = supplier.response();
            AppWriter(Ok(res))
        }
        Err(e) => AppWriter(Err(e)),
    }
}

#[endpoint(tags("supplier"))]
async fn create_supplier(req: JsonBody<SupplierCreate>) -> AppWriter<String> {
    let result = SupplierService::create(req.0).await;
    AppWriter(result)
}

#[endpoint(tags("supplier"))]
async fn update_supplier(req: JsonBody<SupplierUpdate>) -> AppResult<AppWriter<String>> {
    let result = SupplierService::update(req.0).await;
    Ok(AppWriter(result))
}

#[endpoint(tags("supplier"), parameters(("id", description = "user id")))]
async fn delete_supplier(id: PathParam<String>) -> AppWriter<String> {
    let result = SupplierService::delete(id.0).await;
    AppWriter(result)
}
