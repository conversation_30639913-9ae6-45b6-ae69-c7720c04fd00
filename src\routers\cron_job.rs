use crate::{
    app_writer::{<PERSON><PERSON><PERSON><PERSON><PERSON>, App<PERSON>riter},
    db::{ListParams, ListResponse, Page},
    dtos::cron_job::{CronJobCreate, CronJobResponse, CronJobUpdate},
    services::cron_job::CronJobService,
};
use salvo::oapi::{
    endpoint,
    extract::{JsonBody, PathParam},
};
use salvo::{Router, Writer};

pub fn router() -> Router {
    Router::with_path("cron_job")
        .post(create_cron_job)
        .put(update_cron_job)
        .push(
            Router::with_path("<id>")
                .get(get_cron_job_by_id)
                .delete(delete_cron_job)
                .push(Router::with_path("start").post(start_cron_job))
                .push(Router::with_path("stop").post(stop_cron_job))
                .push(Router::with_path("status").post(check_cron_job_status)),
        )
        .push(Router::with_path("list").post(get_cron_job_list))
}

#[endpoint(tags("cron_job"))]
async fn get_cron_job_list(req: JsonBody<ListParams>) -> AppWriter<ListResponse<CronJobResponse>> {
    let page_params = req.0.page.clone();
    let mut page = Page::default();
    if page_params.is_some() {
        page = page_params.unwrap();
    };
    let list = CronJobService::get_list(req.0.clone()).await.unwrap();
    let mut data: Vec<CronJobResponse> = Vec::new();
    for item in list {
        let tmp = item.response();
        data.push(tmp);
    }
    let total = CronJobService::get_total(req.0.params).await.unwrap();
    let res = ListResponse {
        data,
        total,
        page: page.page,
        size: page.limit,
    };

    AppWriter(Ok(res))
}

#[endpoint(tags("cron_job"), parameters(("id", description = "cron_job id for params")))]
async fn get_cron_job_by_id(id: PathParam<String>) -> AppWriter<CronJobResponse> {
    match CronJobService::get_by_id(id.0).await {
        Ok(cron_job) => {
            let res = cron_job.response();
            AppWriter(Ok(res))
        }
        Err(e) => AppWriter(Err(e)),
    }
}

#[endpoint(tags("cron_job"))]
async fn create_cron_job(req: JsonBody<CronJobCreate>) -> AppWriter<String> {
    let result = CronJobService::create(req.0).await;
    AppWriter(result)
}

#[endpoint(tags("cron_job"))]
async fn update_cron_job(req: JsonBody<CronJobUpdate>) -> AppResult<AppWriter<String>> {
    let result = CronJobService::update(req.0).await;
    Ok(AppWriter(result))
}

#[endpoint(tags("cron_job"), parameters(("id", description = "user id")))]
async fn delete_cron_job(id: PathParam<String>) -> AppWriter<String> {
    let result = CronJobService::delete(id.0).await;
    AppWriter(result)
}

#[endpoint(tags("cron_job"), parameters(("id", description = "cron_job id for params")))]
async fn start_cron_job(id: PathParam<String>) -> AppWriter<String> {
    let result = CronJobService::start(id.0).await;
    AppWriter(result)
}

#[endpoint(tags("cron_job"), parameters(("id", description = "cron_job id for params")))]
async fn stop_cron_job(id: PathParam<String>) -> AppWriter<String> {
    let result = CronJobService::stop(id.0).await;
    AppWriter(result)
}

#[endpoint(tags("cron_job"), parameters(("id", description = "cron_job id for params")))]
async fn check_cron_job_status(id: PathParam<String>) -> AppWriter<String> {
    let result = CronJobService::check_status(id.0).await;
    AppWriter(result)
}
