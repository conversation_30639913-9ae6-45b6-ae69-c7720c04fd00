use crate::{
    app_writer::{App<PERSON><PERSON><PERSON>, AppWriter},
    db::{ListParams, ListResponse, Page},
    dtos::product_attribute::{
        ProductAttributeCreate, ProductAttributeResponse, ProductAttributeUpdate,
    },
    services::product_attribute::ProductAttributeService,
};
use salvo::oapi::{
    endpoint,
    extract::{JsonBody, PathParam},
};
use salvo::{Router, Writer};

pub fn router() -> Router {
    Router::with_path("product_attribute")
        .post(create_product_attribute)
        .put(update_product_attribute)
        .push(
            Router::with_path("<id>")
                .get(get_product_attribute_by_id)
                .delete(delete_product_attribute),
        )
        .push(Router::with_path("list").post(get_product_attribute_list))
}

#[endpoint(tags("product_attribute"))]
async fn get_product_attribute_list(
    req: JsonBody<ListParams>,
) -> AppWriter<ListResponse<ProductAttributeResponse>> {
    let page_params = req.0.page.clone();
    let mut page = Page::default();
    if page_params.is_some() {
        page = page_params.unwrap();
    };
    let list = ProductAttributeService::get_list(req.0.clone())
        .await
        .unwrap();
    let mut data: Vec<ProductAttributeResponse> = Vec::new();
    for item in list {
        let tmp = item.response().await;
        data.push(tmp);
    }
    let total = ProductAttributeService::get_total(req.0.params)
        .await
        .unwrap();
    let res = ListResponse {
        data,
        total,
        page: page.page,
        size: page.limit,
    };

    AppWriter(Ok(res))
}

#[endpoint(tags("product_attribute"), parameters(("id", description = "product_attribute id for params")))]
async fn get_product_attribute_by_id(id: PathParam<String>) -> AppWriter<ProductAttributeResponse> {
    match ProductAttributeService::get_by_id(id.0).await {
        Ok(product_attribute) => {
            let res = product_attribute.response().await;
            AppWriter(Ok(res))
        }
        Err(e) => AppWriter(Err(e)),
    }
}

#[endpoint(tags("product_attribute"))]
async fn create_product_attribute(req: JsonBody<ProductAttributeCreate>) -> AppWriter<String> {
    let result = ProductAttributeService::create(req.0).await;
    AppWriter(result)
}

#[endpoint(tags("product_attribute"))]
async fn update_product_attribute(
    req: JsonBody<ProductAttributeUpdate>,
) -> AppResult<AppWriter<String>> {
    let result = ProductAttributeService::update(req.0).await;
    Ok(AppWriter(result))
}

#[endpoint(tags("product_attribute"), parameters(("id", description = "user id")))]
async fn delete_product_attribute(id: PathParam<String>) -> AppWriter<String> {
    let result = ProductAttributeService::delete(id.0).await;
    AppWriter(result)
}
