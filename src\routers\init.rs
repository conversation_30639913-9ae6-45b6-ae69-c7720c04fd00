use crate::{
    app_writer::AppWriter,
    db,
    dtos::{role::RoleCreate, user::UserCreate},
    middleware::init::init_check,
    services,
};
use anyhow::anyhow;
use salvo::{oapi::endpoint, prelude::*, Depot};

pub fn router() -> Router {
    Router::with_path("/init")
        .push(Router::with_path("/db").post(init_db))
        .hoop(init_check)
}

#[endpoint(tags("common"))]
async fn init_db(depot: &mut Depot) -> AppWriter<String> {
    let init_user = depot.get::<String>("init_user").unwrap();
    if init_user.is_empty() {
        return AppWriter(Err(anyhow!("init_user is required").into()));
    }
    if init_user != "ferychen" {
        return AppWriter(Err(anyhow!("init_user is invalid").into()));
    }
    let init_pwd = depot.get::<String>("init_pwd").unwrap();
    if init_pwd.is_empty() {
        return AppWriter(Err(anyhow!("init_pwd is required").into()));
    }
    if init_pwd != "123$%^aB" {
        return AppWriter(Err(anyhow!("init_pwd is invalid").into()));
    }
    // 插入数据库清空表，初始化的操作
    let role_normal = RoleCreate {
        name: "普通用户".to_string(),
        code: Some("normal_user".to_string()),
        desc: Some("系统初始化默认角色".to_string()),
        stable: true,
        order: 2,
    };
    let _res =
        crate::services::role::RoleService::create(db::CreateParams { data: role_normal }).await;
    let role_admin = RoleCreate {
        name: "超级管理员".to_string(),
        code: Some("administrator".to_string()),
        desc: Some("系统初始化默认角色".to_string()),
        stable: true,
        order: 1,
    };
    let _res =
        crate::services::role::RoleService::create(db::CreateParams { data: role_admin }).await;
    let admin_init = UserCreate {
        login_name: "admin".to_string(),
        login_pwd: "123$%^aB".to_string(),
        role_id: Some(_res.unwrap()),
        is_admin: true,
        username: "系统管理员".to_string(),
        is_active: true,
        company_id: None,
        ..Default::default()
    };
    let admin_res =
        crate::services::user::UserService::create(db::CreateParams { data: admin_init }).await;
    if admin_res.is_err() {
        log::error!("init admin user error: {:?}", admin_res);
    } else {
        log::info!("init admin user success");
    }
    init_permission().await;
    init_menu().await;
    let _ = services::permission::PermissionService::init_permission().await;
    AppWriter(Ok("init db success".to_string()))
}

async fn init_permission() {
    for item in PERMISSION_LIST {
        let code = format!("{}:{}:{}", item.3, item.1, item.2);
        let _ = crate::services::permission::PermissionService::create(db::CreateParams {
            data: crate::dtos::permission::PermissionCreate {
                name: item.0.to_string(),
                code: Some(code),
                path: Some(item.2.to_string()),
                method: Some(item.1.to_string()),
                backup: Some("系统初始化创建".to_string()),
                group: Some(item.3.to_string()),
            },
        })
        .await;
    }
}

async fn init_menu() {
    for item in MENU_LIST {
        let order: i32 = if let Ok(num) = item.1.to_string().parse::<i32>() {
            num
        } else {
            0
        };
        let _ = crate::services::menu::MenuService::create(db::CreateParams {
            data: crate::dtos::menu::MenuCreate {
                name: item.0.to_string(),
                order,
                path: Some(item.2.to_string()),
                component: Some(item.3.to_string()),
                redirect: Some(item.4.to_string()),
                active: Some(item.5.to_string()),
                title: Some(item.6.to_string()),
                icon: Some(item.7.to_string()),
                keep_alive: Some(item.8.to_string()),
                hidden: Some(item.9.to_string()),
                is_link: Some(item.10.to_string()),
                parent: Some(item.11.to_string()),
                remark: Some(item.12.to_string()),
            },
        })
        .await;
    }
}

const PERMISSION_LIST: [(&str, &str, &str, &str); 107] = [
    ("附件列表", "POST", "/api/attachment/list", "attachment"),
    ("附件详情", "GET", "/api/attachment", "attachment"),
    ("附件创建", "POST", "/api/attachment", "attachment"),
    ("附件更新", "PUT", "/api/attachment", "attachment"),
    ("附件删除", "DELETE", "/api/attachment", "attachment"),
    ("产品目录列表", "POST", "/api/category/list", "category"),
    ("产品目录详情", "GET", "/api/category", "category"),
    ("产品目录创建", "POST", "/api/category", "category"),
    ("产品目录更新", "PUT", "/api/category", "category"),
    ("产品目录删除", "DELETE", "/api/category", "category"),
    (
        "系统字典列表",
        "POST",
        "/api/config_dict/list",
        "config_dict",
    ),
    ("系统字典详情", "GET", "/api/config_dict", "config_dict"),
    ("系统字典创建", "POST", "/api/config_dict", "config_dict"),
    ("系统字典更新", "PUT", "/api/config_dict", "config_dict"),
    ("系统字典删除", "DELETE", "/api/config_dict", "config_dict"),
    (
        "合同列表",
        "POST",
        "/api/financial_contract/list",
        "financial_contract",
    ),
    (
        "合同详情",
        "GET",
        "/api/financial_contract",
        "financial_contract",
    ),
    (
        "合同创建",
        "POST",
        "/api/financial_contract",
        "financial_contract",
    ),
    (
        "合同更新",
        "PUT",
        "/api/financial_contract",
        "financial_contract",
    ),
    (
        "合同删除",
        "DELETE",
        "/api/financial_contract",
        "financial_contract",
    ),
    ("企业列表", "POST", "/api/company/list", "company"),
    ("企业详情", "GET", "/api/company", "company"),
    ("企业创建", "POST", "/api/company", "company"),
    ("企业更新", "PUT", "/api/company", "company"),
    ("企业删除", "DELETE", "/api/company", "company"),
    (
        "产品属性列表",
        "POST",
        "/api/product_attribute/list",
        "product_attribute",
    ),
    (
        "产品属性详情",
        "GET",
        "/api/product_attribute",
        "product_attribute",
    ),
    (
        "产品属性创建",
        "POST",
        "/api/product_attribute",
        "product_attribute",
    ),
    (
        "产品属性更新",
        "PUT",
        "/api/product_attribute",
        "product_attribute",
    ),
    (
        "产品属性删除",
        "DELETE",
        "/api/product_attribute",
        "product_attribute",
    ),
    (
        "产品SKU列表",
        "POST",
        "/api/product_sku/list",
        "product_sku",
    ),
    ("产品SKU详情", "GET", "/api/product_sku", "product_sku"),
    ("产品SKU创建", "POST", "/api/product_sku", "product_sku"),
    ("产品SKU更新", "PUT", "/api/product_sku", "product_sku"),
    ("产品SKU删除", "DELETE", "/api/product_sku", "product_sku"),
    (
        "采购订单列表",
        "POST",
        "/api/purchase_order/list",
        "purchase_order",
    ),
    (
        "采购订单详情",
        "GET",
        "/api/purchase_order",
        "purchase_order",
    ),
    (
        "采购订单创建",
        "POST",
        "/api/purchase_order",
        "purchase_order",
    ),
    (
        "采购订单更新",
        "PUT",
        "/api/purchase_order",
        "purchase_order",
    ),
    (
        "采购订单删除",
        "DELETE",
        "/api/purchase_order",
        "purchase_order",
    ),
    (
        "采购订单明细列表",
        "POST",
        "/api/purchase_order_info/list",
        "purchase_order_info",
    ),
    (
        "采购订单明细详情",
        "GET",
        "/api/purchase_order_info",
        "purchase_order_info",
    ),
    (
        "采购订单明细创建",
        "POST",
        "/api/purchase_order_info",
        "purchase_order_info",
    ),
    (
        "采购订单明细更新",
        "PUT",
        "/api/purchase_order_info",
        "purchase_order_info",
    ),
    (
        "采购订单明细删除",
        "DELETE",
        "/api/purchase_order_info",
        "purchase_order_info",
    ),
    (
        "销售订单列表",
        "POST",
        "/api/sales_order/list",
        "sales_order",
    ),
    ("销售订单详情", "GET", "/api/sales_order", "sales_order"),
    ("销售订单创建", "POST", "/api/sales_order", "sales_order"),
    ("销售订单更新", "PUT", "/api/sales_order", "sales_order"),
    ("销售订单删除", "DELETE", "/api/sales_order", "sales_order"),
    (
        "销售订单明细列表",
        "POST",
        "/api/sales_order_info/list",
        "sales_order_info",
    ),
    (
        "销售订单明细详情",
        "GET",
        "/api/sales_order_info",
        "sales_order_info",
    ),
    (
        "销售订单明细创建",
        "POST",
        "/api/sales_order_info",
        "sales_order_info",
    ),
    (
        "销售订单明细更新",
        "PUT",
        "/api/sales_order_info",
        "sales_order_info",
    ),
    (
        "销售订单明细删除",
        "DELETE",
        "/api/sales_order_info",
        "sales_order_info",
    ),
    (
        "合同额度列表",
        "POST",
        "/api/quota_contract/list",
        "quota_contract",
    ),
    (
        "合同额度详情",
        "GET",
        "/api/quota_contract",
        "quota_contract",
    ),
    (
        "合同额度创建",
        "POST",
        "/api/quota_contract",
        "quota_contract",
    ),
    (
        "合同额度更新",
        "PUT",
        "/api/quota_contract",
        "quota_contract",
    ),
    (
        "合同额度删除",
        "DELETE",
        "/api/quota_contract",
        "quota_contract",
    ),
    ("库存列表", "POST", "/api/stock/list", "stock"),
    ("库存详情", "GET", "/api/stock", "stock"),
    ("库存创建", "POST", "/api/stock", "stock"),
    ("库存更新", "PUT", "/api/stock", "stock"),
    ("库存删除", "DELETE", "/api/stock", "stock"),
    ("供应商列表", "POST", "/api/supplier/list", "supplier"),
    ("供应商详情", "GET", "/api/supplier", "supplier"),
    ("供应商创建", "POST", "/api/supplier", "supplier"),
    ("供应商更新", "PUT", "/api/supplier", "supplier"),
    ("供应商删除", "DELETE", "/api/supplier", "supplier"),
    ("仓库列表", "POST", "/api/warehouse/list", "warehouse"),
    ("仓库详情", "GET", "/api/warehouse", "warehouse"),
    ("仓库创建", "POST", "/api/warehouse", "warehouse"),
    ("仓库更新", "PUT", "/api/warehouse", "warehouse"),
    ("仓库删除", "DELETE", "/api/warehouse", "warehouse"),
    (
        "仓库位置列表",
        "POST",
        "/api/warehouse_position/list",
        "warehouse_position",
    ),
    (
        "仓库位置详情",
        "GET",
        "/api/warehouse_position",
        "warehouse_position",
    ),
    (
        "仓库位置创建",
        "POST",
        "/api/warehouse_position",
        "warehouse_position",
    ),
    (
        "仓库位置更新",
        "PUT",
        "/api/warehouse_position",
        "warehouse_position",
    ),
    (
        "仓库位置删除",
        "DELETE",
        "/api/warehouse_position",
        "warehouse_position",
    ),
    ("上传单文件", "POST", "/api/upload", "upload"),
    ("上传多文件", "POST", "/api/upload/multi", "upload"),
    ("上传临时文件", "POST", "/api/upload/temp", "upload"),
    ("更新临时文件", "PUT", "/api/upload", "upload"),
    ("获取用户列表", "POST", "/api/user/list", "user"),
    ("获取用户详情", "GET", "/api/user", "user"),
    ("添加用户信息", "POST", "/api/user", "user"),
    ("修改用户信息", "PUT", "/api/user", "user"),
    ("删除用户信息", "DELETE", "/api/user", "user"),
    ("获取当前用户", "GET", "/api/current/user", "current"),
    ("更新当前用户", "PUT", "/api/current/user", "current"),
    ("获取系统菜单", "POST", "/api/menu/list", "menu"),
    ("获取菜单详情", "GET", "/api/menu", "menu"),
    ("添加系统菜单", "POST", "/api/menu", "menu"),
    ("修改系统菜单", "PUT", "/api/menu", "menu"),
    ("删除系统菜单", "DELETE", "/api/menu", "menu"),
    ("获取当前用户菜单", "GET", "/api/current/menu", "current"),
    ("获取角色列表", "POST", "/api/role/list", "role"),
    ("获取角色详情", "GET", "/api/role", "role"),
    ("添加角色信息", "POST", "/api/role", "role"),
    ("修改角色信息", "PUT", "/api/role", "role"),
    ("删除角色信息", "DELETE", "/api/role", "role"),
    ("获取权限列表", "POST", "/api/permission/list", "permission"),
    ("获取权限详情", "GET", "/api/permission", "permission"),
    ("添加权限信息", "POST", "/api/permission", "permission"),
    ("修改权限信息", "PUT", "/api/permission", "permission"),
    ("删除权限信息", "DELETE", "/api/permission", "permission"),
];

const MENU_LIST: [(
    &str,
    &str,
    &str,
    &str,
    &str,
    &str,
    &str,
    &str,
    &str,
    &str,
    &str,
    &str,
    &str,
); 18] = [
    (
        "contractList",
        "1",
        "/contract/list",
        "pages/contract/index",
        "",
        "yes",
        "ContractList",
        "group",
        "no",
        "no",
        "no",
        "''",
        "合同列表",
    ),
    (
        "contractDetail",
        "1",
        "/contract/detail",
        "pages/contract/detail",
        "",
        "yes",
        "ContractDetail",
        "group",
        "yes",
        "yes",
        "no",
        "contractList",
        "合同详情",
    ),
    (
        "orderList",
        "2",
        "/order/list",
        "pages/order/index",
        "",
        "yes",
        "OrderList",
        "group",
        "no",
        "no",
        "no",
        "''",
        "服务订单列表",
    ),
    (
        "orderDetail",
        "1",
        "/order/detail",
        "pages/order/detail",
        "",
        "yes",
        "orderDetail",
        "group",
        "yes",
        "yes",
        "no",
        "orderList",
        "订单详情",
    ),
    (
        "categoryList",
        "3",
        "/category/list",
        "pages/category/index",
        "",
        "yes",
        "CategoryList",
        "group",
        "no",
        "no",
        "no",
        "''",
        "文章目录",
    ),
    (
        "productSkuList",
        "4",
        "/productSku/list",
        "pages/productSku/index",
        "",
        "yes",
        "ProductSkuList",
        "group",
        "no",
        "no",
        "no",
        "''",
        "产品管理",
    ),
    (
        "productSkuDetail",
        "1",
        "/productSku/detail",
        "pages/productSku/detail",
        "",
        "yes",
        "ProductSkuDetail",
        "group",
        "yes",
        "yes",
        "no",
        "productSkuList",
        "产品详情",
    ),
    (
        "supplierList",
        "5",
        "/supplier/list",
        "pages/supplier/index",
        "",
        "yes",
        "SupplierList",
        "group",
        "no",
        "no",
        "no",
        "''",
        "供应商列表",
    ),
    (
        "warehouseList",
        "6",
        "/warehouse/list",
        "pages/warehouse/index",
        "",
        "yes",
        "WarehouseList",
        "group",
        "no",
        "no",
        "no",
        "''",
        "仓库列表",
    ),
    (
        "warehouseDetail",
        "1",
        "/warehouse/detail",
        "pages/warehouse/detail",
        "",
        "yes",
        "WarehouseDetail",
        "group",
        "yes",
        "yes",
        "no",
        "WarehouseList",
        "仓库详情",
    ),
    (
        "stockList",
        "7",
        "/stock/list",
        "pages/stock/index",
        "",
        "yes",
        "StockList",
        "group",
        "no",
        "no",
        "no",
        "''",
        "库存列表",
    ),
    (
        "stockDetail",
        "1",
        "/stock/detail",
        "pages/stock/detail",
        "",
        "yes",
        "StockDetail",
        "group",
        "yes",
        "yes",
        "no",
        "StockList",
        "库存详情",
    ),
    (
        "companyList",
        "95",
        "/company/list",
        "pages/company/index",
        "",
        "yes",
        "CompanyList",
        "group",
        "no",
        "no",
        "no",
        "''",
        "企业列表",
    ),
    (
        "menuList",
        "96",
        "/menu/list",
        "pages/menu/index",
        "",
        "yes",
        "MenuList",
        "group",
        "no",
        "no",
        "no",
        "''",
        "菜单列表",
    ),
    (
        "permissionList",
        "97",
        "/permission/list",
        "pages/permission/index",
        "",
        "yes",
        "PermissionList",
        "group",
        "no",
        "no",
        "no",
        "''",
        "权限列表",
    ),
    (
        "roleList",
        "98",
        "/role/list",
        "pages/role/index",
        "",
        "yes",
        "RoleList",
        "group",
        "no",
        "no",
        "no",
        "'",
        "角色列表",
    ),
    (
        "userList",
        "99",
        "/user/list",
        "pages/user/index",
        "",
        "yes",
        "UserList",
        "123",
        "no",
        "no",
        "no",
        "''",
        "用户列表",
    ),
    (
        "userDetail",
        "1",
        "/user/detail",
        "pages/user/detail",
        "",
        "yes",
        "UserDetail",
        "123",
        "yes",
        "",
        "no",
        "userList",
        "用户详情",
    ),
];
