<template>
  <base-content scrollable>
    <div v-if="itemId" class="row">
        <q-tabs
        v-model="tab"
        dense
        class="text-grey q-pa-sm"
        active-color="primary"
        indicator-color="primary"
        align="start"

      >
        <q-tab name="summary" label="总览" />
        <q-tab name="order" label="订单列表" />
        <q-tab name="content" label="合同内容" />
      </q-tabs>
    </div>
    <div>
      <q-tab-panels v-model="tab" animated>
        <q-tab-pane name="summary">
          <Summary v-if="tab === 'summary' && itemDetail.id" :itemId="itemDetail.id" :itemDetail="itemDetail" />
        </q-tab-pane>
        <q-tab-pane name="order">
          <OrderList v-if="tab === 'order' && itemDetail.id" :itemId="itemDetail.id" />
        </q-tab-pane>
        <q-tab-pane name="content">
          <Content v-if="tab === 'content' && itemDetail.id" :itemId="itemDetail.id" :itemDetail="itemDetail" />
        </q-tab-pane>
      </q-tab-panels>
    </div>
  </base-content>
</template>

<script setup>
import BaseContent from "src/components/BaseContent/BaseContent.vue";
import { Notify } from "quasar";
import { ref, onMounted } from "vue";
import { getActionByPath } from "src/api/manage";
import Summary from "./summary.vue";
import Content from "./content.vue";
import OrderList from "./order_list.vue"
import { useRoute, useRouter } from "vue-router";
const route = useRoute();
const tab = ref("summary");
const itemId = ref();
const itemDetail = ref({});

const url = {
  item: "/api/financial_contract",
};

onMounted(async () => {
  console.log(route.fullPath, "当前route");
  if (route.query.id) {
    itemId.value = route.query.id;
    await handleDetail(itemId.value);
  } else {
    itemId.value = "";
    Notify.create({
      type: "warning",
      message: "信息查询失败，请重试",
      position: "top-right",
    });
  }
});

const handleDetail = async (id) => {
  const { code, data } = await getActionByPath(url.item, [id]);
  if (code === 200) {
    itemDetail.value = data;
    Notify.create({
      type: "positive",
      message: "信息查询成功",
      position: "top-right",
    });
  } else {
    itemDetail.value = {};
    Notify.create({
      type: "warning",
      message: "信息查询失败，请重试",
      position: "top-right",
    });
  }
};
</script>
