use crate::db::{Castable, Creatable, Patchable};
use salvo::prelude::{Extractible, ToSchema};
use serde::{Deserialize, Serialize};
use std::collections::{HashMap, VecDeque};
use validator::Validate;

#[derive(Default, Deserialize, Serialize, Debug, Validate, ToSchema)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct MenuCreate {
    pub name: String,
    pub order: i32,
    pub path: Option<String>,
    pub component: Option<String>,
    pub redirect: Option<String>,
    pub active: Option<String>,
    pub title: Option<String>,
    pub icon: Option<String>,
    pub keep_alive: Option<String>,
    pub hidden: Option<String>,
    pub is_link: Option<String>,
    pub parent: Option<String>,
    pub remark: Option<String>,
}

impl Creatable for MenuCreate {}

#[derive(Default, Debug, Deserialize, Serialize, Extractible, ToSchema)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct MenuUpdate {
    #[salvo(extract(source(from = "param")))]
    pub id: String,
    pub name: String,
    pub order: i32,
    pub path: Option<String>,
    pub component: Option<String>,
    pub redirect: Option<String>,
    pub active: Option<String>,
    pub title: Option<String>,
    pub icon: Option<String>,
    pub keep_alive: Option<String>,
    pub hidden: Option<String>,
    pub is_link: Option<String>,
    pub parent: Option<String>,
    pub remark: Option<String>,
}

impl Patchable for MenuUpdate {}

#[derive(Debug, Deserialize, Serialize, ToSchema, Default, Clone)]
pub struct MenuResponse {
    pub id: String,
    pub name: String,
    pub order: i32,
    pub path: Option<String>,
    pub component: Option<String>,
    pub redirect: Option<String>,
    pub active: Option<String>,
    pub title: Option<String>,
    pub icon: Option<String>,
    pub keep_alive: Option<String>,
    pub hidden: Option<String>,
    pub is_link: Option<String>,
    pub parent: Option<String>,
    pub remark: Option<String>,
    pub children: Option<Vec<MenuResponse>>,
}

impl Castable for MenuResponse {}

impl MenuResponse {
    pub fn array_to_tree(items: Vec<MenuResponse>) -> Vec<MenuResponse> {
        let mut map: HashMap<String, Vec<MenuResponse>> = HashMap::new();
        let mut id_map: HashMap<String, bool> = HashMap::new(); // 新增ID映射表
        let mut result = Vec::new();
        let mut stack = VecDeque::new();

        // 第一步：遍历items，构建map和根节点
        for item in items {
            let parent_id = item.parent.clone().unwrap_or_default();
            id_map.insert(item.name.clone(), true); // 初始化ID映射表
            if parent_id.is_empty() || parent_id == "''" {
                // 根节点直接加入结果和栈
                result.push(item.clone());
                stack.push_back(item.clone());
            } else {
                // 非根节点存入map
                map.entry(parent_id).or_insert_with(Vec::new).push(item);
            }
        }

        // 第二步：使用栈处理所有节点
        while let Some(mut current_node) = stack.pop_front() {
            if let Some(children) = map.get(&current_node.name) {
                // 处理子节点
                for child in children {
                    // 如果子节点还有子节点，加入栈中继续处理
                    if map.contains_key(&child.name) && *id_map.get(&child.name).unwrap() {
                        println!("子节点：{:?}", child);
                        stack.push_back(child.clone());
                    }
                    // 将子节点加入当前节点的children
                    if let Some(existing_children) = &mut current_node.children {
                        existing_children.push(child.clone());
                    } else {
                        current_node.children = Some(vec![child.clone()]);
                    }
                }
            } else {
                // 如果当前节点没有子节点了，标记为已完成
                id_map.insert(current_node.name.clone(), false);
            }

            // 更新结果中的节点
            if let Some(root_node) = result
                .iter_mut()
                .find(|node| node.name == current_node.name)
            {
                *root_node = current_node.clone();
            }

            // 检查当前节点的所有子节点是否都已完成
            if let Some(children) = &current_node.children {
                let all_children_done = children
                    .iter()
                    .all(|child| !*id_map.get(&child.name).unwrap());
                if !all_children_done {
                    id_map.insert(current_node.name.clone(), false);
                    // 更新当前节点在父节点中的信息
                    if let Some(parent_id) = &current_node.parent {
                        // 先找到父节点
                        let parent_index = result.iter().position(|node| node.name == *parent_id);

                        if let Some(index) = parent_index {
                            // 获取父节点的可变引用
                            if let Some(parent_node) = result.get_mut(index) {
                                if let Some(parent_children) = &mut parent_node.children {
                                    if let Some(existing_child) = parent_children
                                        .iter_mut()
                                        .find(|c| c.name == current_node.name.clone())
                                    {
                                        // 用当前节点替换父节点中的旧数据
                                        *existing_child = current_node.clone();
                                    }
                                }
                            }
                        }
                    }
                } else {
                    stack.push_back(current_node.clone());
                }
            }
        }

        // 对结果进行排序
        result.sort_by_key(|node| node.order);
        result
    }
    pub fn array_to_tree_v2(items: Vec<MenuResponse>) -> Vec<MenuResponse> {
        // 内部帮助函数，处理节点更新
        fn update_node_in_parent(
            result: &mut Vec<MenuResponse>,
            current_node: &MenuResponse,
            parent_id: &str,
        ) {
            if let Some(parent) = result.iter_mut().find(|node| node.name == parent_id) {
                if let Some(children) = &mut parent.children {
                    if let Some(existing) =
                        children.iter_mut().find(|c| c.name == current_node.name)
                    {
                        *existing = current_node.clone();
                    }
                }
            }
        }

        // 初始化数据结构
        let mut node_map: HashMap<String, Vec<MenuResponse>> = HashMap::new();
        let mut processing = HashMap::new(); // 记录节点处理状态
        let mut result = Vec::new();
        let mut queue = VecDeque::new();

        // 第一步：分类节点
        for item in items {
            let parent_id = item.parent.clone().unwrap_or_default();
            processing.insert(item.name.clone(), true);

            if parent_id.is_empty() || parent_id == "''" {
                result.push(item.clone());
                queue.push_back(item);
            } else {
                node_map.entry(parent_id).or_default().push(item);
            }
        }

        // 第二步：构建树结构
        while let Some(mut current) = queue.pop_front() {
            // 处理子节点
            if let Some(children) = node_map.get(&current.name) {
                let mut child_nodes = Vec::new();

                for child in children {
                    child_nodes.push(child.clone());

                    if node_map.contains_key(&child.name) && processing[&child.name] {
                        queue.push_back(child.clone());
                    }
                }

                current.children = Some(child_nodes);
            } else {
                processing.insert(current.name.clone(), false);
            }

            // 更新当前节点
            if let Some(node) = result.iter_mut().find(|n| n.name == current.name) {
                *node = current.clone();
            }

            // 检查子节点状态并更新父节点
            if let Some(children) = &current.children {
                let all_processed = children
                    .iter()
                    .all(|child| !processing.get(&child.name).unwrap_or(&false));

                if !all_processed {
                    processing.insert(current.name.clone(), false);

                    // 更新父节点中的信息
                    if let Some(parent_id) = &current.parent {
                        update_node_in_parent(&mut result, &current, parent_id);
                    }
                } else {
                    queue.push_back(current);
                }
            }
        }

        // 最终排序
        result.sort_by_key(|node| node.order);
        result
    }
}

// Third pass: remove children from root nodes that have parents
