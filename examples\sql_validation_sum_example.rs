// SQL验证版本的 exec_sum() 函数使用示例
// 展示通过SQL测试字段类型的新功能

use crate::{
    db::{Database, WhereOptions},
    services::sales_order::SalesOrderService,
};

/// 展示SQL验证版本与快速版本的对比
pub async fn sql_validation_vs_fast_comparison() {
    println!("=== SQL验证版本 vs 快速版本对比 ===\n");

    let test_fields = vec![
        ("total_payment", "总支付金额", true),
        ("amount", "商品金额", true),
        ("express_fee", "运费", true),
        ("customer", "客户姓名", false),
        ("status", "订单状态", false),
        ("non_existent_field", "不存在的字段", false),
    ];

    for (field, description, should_succeed) in test_fields {
        println!("测试字段: {} ({})", field, description);
        
        // 测试SQL验证版本
        println!("  1. SQL验证版本 (exec_sum):");
        let params = vec![];
        let start = std::time::Instant::now();
        match SalesOrderService::sum_field(params.clone(), field).await {
            Ok(result) => {
                let duration = start.elapsed();
                println!("     ✓ 成功: {} (耗时: {:?})", result, duration);
            }
            Err(e) => {
                let duration = start.elapsed();
                if should_succeed {
                    println!("     ✗ 意外失败: {} (耗时: {:?})", e, duration);
                } else {
                    println!("     ✓ 正确拒绝: 字段验证失败 (耗时: {:?})", duration);
                }
            }
        }

        // 测试快速版本
        println!("  2. 快速版本 (exec_sum_fast):");
        let start = std::time::Instant::now();
        match SalesOrderService::sum_field_fast(params, field).await {
            Ok(result) => {
                let duration = start.elapsed();
                println!("     ✓ 成功: {} (耗时: {:?})", result, duration);
            }
            Err(e) => {
                let duration = start.elapsed();
                if should_succeed {
                    println!("     ✗ 意外失败: {} (耗时: {:?})", e, duration);
                } else {
                    println!("     ✓ 正确拒绝: 字段验证失败 (耗时: {:?})", duration);
                }
            }
        }
        println!();
    }
}

/// 展示SQL验证的详细过程
pub async fn sql_validation_process_demo() {
    println!("=== SQL验证过程演示 ===\n");

    let test_cases = vec![
        ("有效数值字段", "total_payment"),
        ("有效整数字段", "created_at"),
        ("无效文本字段", "customer"),
        ("不存在的字段", "fake_amount_field"),
    ];

    for (case_name, field) in test_cases {
        println!("测试案例: {} - 字段: {}", case_name, field);
        
        // 直接调用Database层方法以查看详细的SQL执行过程
        let params = vec![];
        match Database::exec_sum("sales_order", params, field).await {
            Ok(result) => {
                println!("  ✓ 验证通过并执行成功: {}", result);
            }
            Err(e) => {
                println!("  ✗ 验证失败或执行失败: {}", e);
            }
        }
        println!();
    }
}

/// 展示不同数据类型字段的验证
pub async fn different_data_types_validation() {
    println!("=== 不同数据类型字段验证 ===\n");

    let data_type_tests = vec![
        ("Decimal类型", "total_payment"),
        ("Decimal类型", "amount"),
        ("Decimal类型", "express_fee"),
        ("整数类型", "created_at"),
        ("整数类型", "updated_at"),
        ("字符串类型", "customer"),
        ("字符串类型", "status"),
        ("字符串类型", "platform_name"),
        ("可选字符串", "address"),
        ("可选字符串", "pay_type"),
    ];

    for (data_type, field) in data_type_tests {
        println!("测试 {} 字段: {}", data_type, field);
        
        let params = vec![];
        match Database::exec_sum("sales_order", params, field).await {
            Ok(result) => {
                println!("  ✓ {} 可以进行汇总: {}", data_type, result);
            }
            Err(e) => {
                println!("  ✗ {} 不能进行汇总: {}", data_type, e);
            }
        }
    }
}

/// 展示复杂查询条件下的SQL验证
pub async fn complex_conditions_with_validation() {
    println!("\n=== 复杂查询条件下的SQL验证 ===\n");

    let test_scenarios = vec![
        (
            "单条件查询",
            vec![WhereOptions::new("status".to_string(), "completed".to_string())],
            "total_payment"
        ),
        (
            "多条件查询",
            vec![
                WhereOptions::new("status".to_string(), "completed".to_string()),
                WhereOptions::new("platform_name".to_string(), "淘宝".to_string()),
            ],
            "amount"
        ),
        (
            "时间范围查询",
            vec![
                WhereOptions::new("begin_date".to_string(), "2024-01-01".to_string()),
                WhereOptions::new("end_date".to_string(), "2024-12-31".to_string()),
            ],
            "express_fee"
        ),
        (
            "ID列表查询",
            vec![
                WhereOptions::new("ids".to_string(), "sales_order:1,sales_order:2".to_string()),
            ],
            "platform_fee_total"
        ),
    ];

    for (scenario_name, params, field) in test_scenarios {
        println!("场景: {} - 字段: {}", scenario_name, field);
        
        match SalesOrderService::sum_field(params, field).await {
            Ok(result) => {
                println!("  ✓ {}", result);
            }
            Err(e) => {
                println!("  ✗ {}", e);
            }
        }
        println!();
    }
}

/// 性能对比测试
pub async fn performance_comparison_test() {
    println!("=== 性能对比测试 ===\n");

    let test_field = "total_payment";
    let params = vec![WhereOptions::new("status".to_string(), "completed".to_string())];
    let iterations = 5;

    println!("测试字段: {}", test_field);
    println!("测试次数: {}", iterations);
    println!();

    // 测试SQL验证版本
    println!("SQL验证版本性能测试:");
    let mut total_duration = std::time::Duration::new(0, 0);
    for i in 1..=iterations {
        let start = std::time::Instant::now();
        match SalesOrderService::sum_field(params.clone(), test_field).await {
            Ok(_) => {
                let duration = start.elapsed();
                total_duration += duration;
                println!("  第{}次: {:?}", i, duration);
            }
            Err(e) => {
                println!("  第{}次失败: {}", i, e);
            }
        }
    }
    let avg_duration = total_duration / iterations;
    println!("  平均耗时: {:?}", avg_duration);

    println!();

    // 测试快速版本
    println!("快速版本性能测试:");
    let mut total_duration = std::time::Duration::new(0, 0);
    for i in 1..=iterations {
        let start = std::time::Instant::now();
        match SalesOrderService::sum_field_fast(params.clone(), test_field).await {
            Ok(_) => {
                let duration = start.elapsed();
                total_duration += duration;
                println!("  第{}次: {:?}", i, duration);
            }
            Err(e) => {
                println!("  第{}次失败: {}", i, e);
            }
        }
    }
    let avg_duration = total_duration / iterations;
    println!("  平均耗时: {:?}", avg_duration);
}

/// 错误处理详细测试
pub async fn detailed_error_handling_test() {
    println!("\n=== 详细错误处理测试 ===\n");

    let error_test_cases = vec![
        ("不存在的表", "non_existent_table", "amount"),
        ("不存在的字段", "sales_order", "non_existent_field"),
        ("文本字段", "sales_order", "customer"),
        ("状态字段", "sales_order", "status"),
        ("地址字段", "sales_order", "address"),
    ];

    for (error_type, table, field) in error_test_cases {
        println!("错误类型: {} - 表: {}, 字段: {}", error_type, table, field);
        
        let params = vec![];
        match Database::exec_sum(table, params, field).await {
            Ok(result) => {
                println!("  ⚠ 意外成功: {}", result);
            }
            Err(e) => {
                println!("  ✓ 正确处理错误: {}", e);
            }
        }
        println!();
    }
}

/// 主函数示例
pub async fn run_all_sql_validation_examples() {
    println!("🔍 SQL验证版本 exec_sum() 函数完整示例\n");
    println!("=" .repeat(60));
    
    // 运行各种示例
    sql_validation_vs_fast_comparison().await;
    sql_validation_process_demo().await;
    different_data_types_validation().await;
    complex_conditions_with_validation().await;
    performance_comparison_test().await;
    detailed_error_handling_test().await;
    
    println!("=" .repeat(60));
    println!("✅ 所有SQL验证示例运行完成！");
    
    println!("\n📋 总结:");
    println!("1. SQL验证版本提供更准确的字段类型检查");
    println!("2. 快速版本适用于已知字段类型正确的高频调用场景");
    println!("3. 两个版本都支持复杂的查询条件");
    println!("4. 错误信息更加详细和准确");
}
