use crate::db::{Castable, Creatable, Patchable};
use salvo::oapi::ToSchema;
use serde::{Deserialize, Serialize};
use validator::Validate;

#[derive(Deserialize, Serialize, Debug, Validate, ToSchema, Clone)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct UsageCreate {
    pub content: String,
    pub user: Option<String>,
}

impl Creatable for UsageCreate {}

#[derive(Default, Deserialize, Serialize, Debug, Validate, ToSchema)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct UsageUpdate {
    pub id: String,
    pub content: Option<String>,
    pub user: Option<String>,
}

impl Patchable for UsageUpdate {}

#[derive(Debug, Deserialize, Serialize, ToSchema, Default, Clone)]
pub struct UsageResponse {
    pub id: String,
    pub content: String,
    pub user: Option<String>,
}

impl Castable for UsageResponse {}

// impl From<Usage> for UsageResponse {
//     fn from(value: Usage) -> Self {
//         Self {
//             id: value.id.to_string(),
//             content: Some(value.content),
//             user: Some(UserResponse::from(value.user.id.)),
//         }
//     }
// }
