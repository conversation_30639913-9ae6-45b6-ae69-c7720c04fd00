use crate::middleware::{
    jwt::jwt_middleware, permission::check_permission, print_req::print_request,
};
use salvo::{
    prelude::{<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>},
    Router,
};
mod attachment;
mod category;
mod company;
mod config_dict;
mod cron_job;
mod financial_contract;
mod import_record;
mod init;
mod menu;
mod permission;
mod product_attribute;
mod product_sku;
mod public;
mod purchase_order;
mod purchase_order_info;
mod quota_contract;
mod repayment;
mod repayment_log;
mod req_builder;
mod role;
mod sales_order;
mod sales_order_info;
mod static_routers;
mod stock;
mod supplier;
mod upload;
mod user;
mod warehouse;
mod warehouse_position;

pub fn router() -> Router {
    let mut no_auth_routers = vec![public::router(), init::router()];

    let _print_req = print_request;

    let mut need_auth_routers = vec![
        user::router(),
        config_dict::router(),
        financial_contract::router(),
        import_record::router(),
        role::router(),
        menu::router(),
        permission::router(),
        product_attribute::router(),
        company::router(),
        purchase_order::router(),
        purchase_order_info::router(),
        sales_order::router(),
        sales_order_info::router(),
        quota_contract::router(),
        repayment::router(),
        repayment_log::router(),
        attachment::router(),
        upload::router(),
        category::router(),
        product_sku::router(),
        stock::router(),
        supplier::router(),
        warehouse::router(),
        warehouse_position::router(),
        repayment::router(),
        repayment_log::router(),
        req_builder::router(),
        cron_job::router(),
    ];

    let mut static_routers = static_routers::create_static_routers();
    let router = Router::new()
        .hoop(_print_req)
        .hoop(Logger::new())
        .hoop(CatchPanic::new())
        .append(&mut no_auth_routers)
        .push(
            Router::with_path("/api")
                .append(&mut need_auth_routers)
                .hoop(jwt_middleware())
                .hoop(check_permission),
        )
        .append(&mut static_routers);
    let doc = OpenApi::new("manage househole web api", "0.1.0").merge_router(&router);
    router
        .push(doc.into_router("/api-doc/openapi.json"))
        .push(Scalar::new("/api-doc/openapi.json").into_router("scalar"))
}
