use anyhow::anyhow;

use crate::{
    app_writer::AppResult,
    db::{CreateParams, ListParams, RelateParams, UpdateParams, WhereOptions},
    dtos::role::{RoleCreate, RoleUpdate},
    entities::role::{Role, RoleBmc},
};

use super::permission::PermissionService;

pub struct RoleService;
impl RoleService {
    pub async fn get_total(req: Option<Vec<WhereOptions>>) -> AppResult<u32> {
        let mut where_options = Vec::new();
        if let Some(params) = req {
            where_options = params;
        }
        match RoleBmc::get_total(where_options).await {
            Ok(count) => {
                if let Some(count) = count {
                    Ok(count.count)
                } else {
                    Ok(0)
                }
            }
            Err(_) => return Ok(0),
        }
    }

    pub async fn get_list(req: ListParams) -> AppResult<Vec<Role>> {
        let mut limit: u32 = 15;
        let mut offset: u32 = 0;
        if let Some(page) = req.page {
            limit = page.get_limit().unwrap_or(0);
            offset = page.get_offset();
        }
        let mut where_options = Vec::new();
        if let Some(params) = req.params {
            where_options = params;
        }
        let res = RoleBmc::get_list(offset, limit, req.options, where_options).await?;
        Ok(res)
    }

    pub async fn get_list_by_role(req: ListParams, state: String) -> AppResult<Vec<Role>> {
        let mut limit: u32 = 15;
        let mut offset: u32 = 0;
        if let Some(page) = req.page {
            limit = page.get_limit().unwrap_or(0);
            offset = page.get_offset();
        }
        let mut where_options = Vec::new();
        if let Some(params) = req.params {
            where_options = params;
        }
        let res =
            RoleBmc::get_list_by_role(offset, limit, req.options, state, where_options).await?;
        Ok(res)
    }

    pub async fn get_by_query(params: Vec<WhereOptions>) -> AppResult<Option<Role>> {
        let res = RoleBmc::get_by_query(params).await?;
        Ok(res)
    }

    pub async fn get_by_id(id: String) -> AppResult<Role> {
        match RoleBmc::get_by_id(&id).await {
            Ok(role) => {
                if let Some(role) = role {
                    Ok(role)
                } else {
                    Err(anyhow!("Role not found").into())
                }
            }
            Err(e) => Err(e),
        }
    }

    pub async fn relate_to_menu(mut req: RelateParams) -> AppResult<String> {
        let related = RoleBmc::return_item_related(
            req.from.clone(),
            "menu".to_string(),
            "has_menu".to_string(),
        )
        .await?;
        // 1. 创建两个Vec用于存储新增和删除的菜单
        let mut to_add = Vec::new();
        let mut to_remove = Vec::new();

        // 2. 对比req.to和已关联的菜单
        // 找出需要新增的菜单
        for menu in &req.to {
            if !related.contains(menu) {
                to_add.push(menu.clone());
            }
        }

        // 找出需要删除的菜单
        for menu in related {
            if !req.to.contains(&menu) {
                to_remove.push(menu.clone());
                RoleBmc::unrelate_to_item("has_menu", &req.from, &menu).await?;
            }
        }
        // 3. 执行新增和删除操作
        req.to = to_add;
        RoleBmc::relate_to_item(req, "has_menu").await?;
        Ok("Role updated".to_string())
    }

    pub async fn relate_to_permission(mut req: RelateParams) -> AppResult<String> {
        let related = RoleBmc::return_item_related(
            req.from.clone(),
            "permission".to_string(),
            "has_permission".to_string(),
        )
        .await?;
        // 1. 创建两个Vec用于存储新增和删除的权限
        let mut to_add = Vec::new();
        let mut to_remove = Vec::new();

        // 2. 对比req.to和已关联的权限
        // 找出需要新增的权限
        for permission in &req.to {
            if !related.contains(permission) {
                to_add.push(permission.clone());
            }
        }

        // 找出需要删除的权限
        for permission in related {
            if !req.to.contains(&permission) {
                to_remove.push(permission.clone());
                RoleBmc::unrelate_to_item("has_permission", &req.from, &permission).await?;
            }
        }

        // 3. 执行新增操作
        req.to = to_add;
        RoleBmc::relate_to_item(req, "has_permission").await?;

        // 4. 执行权限刷新
        let _ = PermissionService::init_permission().await;

        Ok("Role updated".to_string())
    }

    pub async fn create(req: CreateParams<RoleCreate>) -> AppResult<String> {
        let check = RoleBmc::get_by_query(vec![WhereOptions::new(
            "name".to_string(),
            req.data.name.clone(),
        )])
        .await?;
        if check.is_some() {
            return Err(anyhow!("角色名已存在。").into());
        }
        let res = RoleBmc::create(req.data).await?;
        Ok(res)
    }

    pub async fn update(req: UpdateParams<RoleUpdate>) -> AppResult<String> {
        RoleBmc::update(req.data).await?;
        Ok("Role updated".to_string())
    }

    pub async fn delete(id: String) -> AppResult<String> {
        RoleBmc::delete(id).await?;
        Ok("Role deleted".to_string())
    }
}
