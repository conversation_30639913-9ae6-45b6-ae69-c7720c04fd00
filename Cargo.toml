[package]
name = "fund_manager"
version = "0.1.0"
edition = "2021"

[dependencies]
anyhow = "1.0.79"
argon2 = { version = "0.5.3", features = [] }
clia-tracing-config = "0.2.7"
jsonwebtoken = "9.2.0"
once_cell = "1.19.0"
log = "0.4"
rand = { version = "0.8.5", features = [] }
rust-embed = "8.0.0"
salvo = { version = "0.75.0", features = [
    "anyhow",
    "logging",
    "cors",
    "oapi",
    "jwt-auth",
    "rustls",
    "catch-panic",
    "cookie",
    "serve-static",
    "test",
] }
serde = "1.0.217"
serde_json = "1.0.132"
serde_yaml = "0.9.31"
thiserror = "2.0.9"
time = "0.3.28"
tokio = { version = "1", features = ["full"] }
tokio-cron-scheduler = "*"
tracing = "0.1"
calamine = "0.28.0"
rust_decimal = "1.37.1"
surrealdb = { version = "2.2.1", features = ["kv-rocksdb"] }
chrono = "0.4"
uuid = { version = "1.4.1", features = ["v4", "fast-rng", "macro-diagnostics"] }
validator = { version = "0.19", features = ["derive"] }
captcha = "1.0.0"
redis = { version = "0.29.1", features = ["tokio-comp", "ahash"] }
regex = "1.11.1"
lazy_static = "1.4.0"
md5 = "0.7.0"
path-slash = "0.2.1"
aliyun-oss-rust-sdk = "0.2.1"
qcos = "0.1.13"
reqwest = { version = "0.12.13", features = ["json"] }
