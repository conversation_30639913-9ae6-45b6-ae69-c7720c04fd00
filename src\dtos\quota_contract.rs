use crate::db::{Castable, Creatable, Patchable};
use crate::utils::date::default_now;
use rust_decimal::Decimal;
use salvo::prelude::{Extractible, ToSchema};
use serde::{Deserialize, Serialize};
use validator::Validate;

#[derive(Default, Clone, Deserialize, Serialize, Debug, Validate, ToSchema)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct QuotaContractCreate {
    pub serial: String,
    pub project_name: Option<String>,
    pub contract_name: Option<String>,
    pub contract_type: Option<String>,
    pub contract_time: Option<String>,
    pub desc: Option<String>,
    pub company_id: Option<String>,
    pub company_name: Option<String>,
    pub status: String,
    pub request_quota: Decimal,
    pub used_quota: Decimal,
    pub expect_profit: Decimal,
    #[serde(default = "default_now")]
    pub created_at: i64,
    #[serde(default = "default_now")]
    pub updated_at: i64,
}

impl Creatable for QuotaContractCreate {}

#[derive(Default, Debug, Deserialize, Serialize, Extractible, ToSchema)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct QuotaContractUpdate {
    #[salvo(extract(source(from = "param")))]
    pub id: String,
    pub serial: String,
    pub project_name: Option<String>,
    pub contract_name: Option<String>,
    pub contract_type: Option<String>,
    pub contract_time: Option<String>,
    pub desc: Option<String>,
    pub company_id: Option<String>,
    pub company_name: Option<String>,
    pub status: String,
    pub request_quota: Decimal,
    pub used_quota: Decimal,
    pub expect_profit: Decimal,
    #[serde(default = "default_now")]
    pub created_at: i64,
    #[serde(default = "default_now")]
    pub updated_at: i64,
}

impl Patchable for QuotaContractUpdate {}

#[derive(Debug, Deserialize, Serialize, ToSchema, Default, Clone)]
pub struct QuotaContractResponse {
    pub id: String,
    pub serial: String,
    pub project_name: Option<String>,
    pub contract_name: Option<String>,
    pub contract_type: Option<String>,
    pub contract_time: Option<String>,
    pub desc: Option<String>,
    pub company_id: Option<String>,
    pub company_name: Option<String>,
    pub status: String,
    pub request_quota: Decimal,
    pub used_quota: Decimal,
    pub expect_profit: Decimal,
    pub created_at: i64,
    pub updated_at: i64,
}

impl Castable for QuotaContractResponse {}
