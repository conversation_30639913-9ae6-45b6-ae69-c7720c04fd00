import { Notify } from "quasar";
import { api } from "src/boot/axios";
import { responseData } from "src/types";

export async function getAction<T = any>(
  url: string,
  params?: T,
): Promise<responseData<T>> {
  try {
    const response = await api({
      url: url,
      method: "get",
      params: params,
    });
    return {
      data: response.data.data,
      code: response.data.code,
      msg: response.data.msg,
    };
  } catch (error) {
    const err = error as Error & { response?: { status?: number } };
    return {
      data: undefined,
      code: err.response?.status || 500,
      msg: err.message || "Request Error",
    };
  }
}

export async function getActionByPath<T = any>(
  url: string,
  params: string[],
): Promise<responseData<T>> {
  try {
    const pathParams = params.join("/");
    const response = await api({
      url: url + "/" + pathParams,
      method: "get",
    });
    return {
      data: response.data.data,
      code: response.data.code,
      msg: response.data.msg,
    };
  } catch (error) {
    const err = error as Error & { response?: { status?: number } };
    return {
      data: undefined,
      code: err.response?.status || 500,
      msg: err.message || "Request Error",
    };
  }
}

export async function postAction<T = any>(
  url: string,
  params: T,
): Promise<responseData<T>> {
  try {
    const response = await api({
      url: url,
      method: "post",
      data: params,
    });
    return {
      data: response.data.data,
      code: response.data.code,
      msg: response.data.msg,
    };
  } catch (error) {
    const err = error as Error & { response?: { status?: number } };
    return {
      data: undefined,
      code: err.response?.status || 500,
      msg: err.message || "Request Error",
    };
  }
}

export async function postBlobAction<T = any>(
  url: string,
  params: T,
): Promise<responseData<T>> {
  try {
    const response = await api({
      url: url,
      method: "post",
      data: params,
      responseType: "blob",
    });
    return {
      data: response.data.data,
      code: response.data.code,
      msg: response.data.msg,
    };
  } catch (error) {
    const err = error as Error & { response?: { status?: number } };
    return {
      data: undefined,
      code: err.response?.status || 500,
      msg: err.message || "Request Error",
    };
  }
}

export async function putAction<T = any>(
  url: string,
  params: T,
): Promise<responseData<T>> {
  try {
    const response = await api({
      url: url,
      method: "put",
      data: params,
    });
    return {
      data: response.data.data,
      code: response.data.code,
      msg: response.data.msg,
    };
  } catch (error) {
    const err = error as Error & { response?: { status?: number } };
    return {
      data: undefined,
      code: err.response?.status || 500,
      msg: err.message || "Request Error",
    };
  }
}

export async function deleteAction<T = any>(
  url: string,
  params?: T,
): Promise<responseData<T>> {
  try {
    const response = await api({
      url: url,
      method: "delete",
      data: params,
    });
    return {
      data: response.data.data,
      code: response.data.code,
      msg: response.data.msg,
    };
  } catch (error) {
    const err = error as Error & { response?: { status?: number } };
    return {
      data: undefined,
      code: err.response?.status || 500,
      msg: err.message || "Request Error",
    };
  }
}

export async function deleteActionByPath<T = any>(
  url: string,
  params: string[],
): Promise<responseData<T>> {
  try {
    const pathParams = params.join("/");
    const response = await api({
      url: url + "/" + pathParams,
      method: "delete",
    });
    return {
      data: response.data.data,
      code: response.data.code,
      msg: response.data.msg,
    };
  } catch (error) {
    const err = error as Error & { response?: { status?: number } };
    return {
      data: undefined,
      code: err.response?.status || 500,
      msg: err.message || "Request Error",
    };
  }
}

// post download 方式
export async function downFile(
  url: string,
  params: any,
): Promise<{
  data: Blob;
  headers: Record<string, string>;
}> {
  try {
    const response = await api({
      url: url,
      data: params,
      method: "POST",
      responseType: "blob",
    });
    return {
      data: response.data,
      headers: Object.fromEntries(
        Object.entries(response.headers).map(([key, value]) => [
          key,
          String(value),
        ]),
      ),
    };
  } catch (error) {
    const err = error as Error;
    Notify.create({
      type: "negative",
      message: err.message || "Download Failed",
    });
    throw err;
  }
}

// 专门用于文件上传的函数，支持 multipart/form-data
export async function postFormDataAction<T = any>(
  url: string,
  formData: FormData,
): Promise<responseData<T>> {
  try {
    const response = await api({
      url: url,
      method: "post",
      data: formData,
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    return {
      data: response.data.data,
      code: response.data.code,
      msg: response.data.msg,
    };
  } catch (error) {
    const err = error as Error & { response?: { status?: number } };
    return {
      data: undefined,
      code: err.response?.status || 500,
      msg: err.message || "Request Error",
    };
  }
}

export async function downloadAction(url: string, params: any) {
  const res = await downFile(url, params);
  if (!res) {
    Notify.create({
      type: "negative",
      message: "文件下载失败！",
    });
    return;
  }
  // await downFile(url: string, params: T).then(res => {
  //   console.log(res.headers)
  // })
  const urlHref = window.URL.createObjectURL(new Blob([res.data]));
  const link = document.createElement("a");
  link.style.display = "none";
  link.href = urlHref;
  const fileName =
    res.headers["content-filename"].split("/")[
      res.headers["content-filename"].split("/").length - 1
    ];
  link.setAttribute("download", fileName);
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link); // 下载完成移除元素
  window.URL.revokeObjectURL(urlHref); // 释放掉blob对象
}
