use crate::{
    app_writer::{App<PERSON><PERSON><PERSON>, AppWriter},
    db::{ListParams, WhereOptions},
    dtos::quota_contract::{QuotaContractCreate, QuotaContractResponse, QuotaContractUpdate},
    services::quota_contract::QuotaContractService,
};
use salvo::{
    oapi::{
        endpoint,
        extract::{JsonBody, PathParam},
    },
    Depot, Request,
};
use salvo::{Router, Writer};

pub fn router() -> Router {
    Router::with_path("quota_contract")
        .post(create_quota_contract)
        .put(update_quota_contract)
        .push(
            Router::with_path("<id>")
                .get(get_quota_contract_by_id)
                .delete(delete_quota_contract),
        )
        .push(Router::with_path("<quota_contractname>").get(get_quota_contract_by_name))
        .push(Router::with_path("list").post(get_quota_contract_list))
}

#[endpoint(tags("quota_contract"))]
async fn get_quota_contract_list(
    req: &mut Request,
) -> AppResult<AppWriter<Vec<QuotaContractResponse>>> {
    let req: ListParams = req.extract().await?;
    let list = QuotaContractService::get_list(req).await?;

    let mut res: Vec<QuotaContractResponse> = Vec::new();
    for item in list {
        let tmp = item.response();
        res.push(tmp);
    }

    Ok(AppWriter(Ok(res)))
}

#[endpoint(tags("quota_contract"), parameters(("id", description = "quota_contract id for params")))]
async fn get_quota_contract_by_id(id: PathParam<String>) -> AppWriter<QuotaContractResponse> {
    match QuotaContractService::get_by_id(id.0).await {
        Ok(quota_contract) => {
            let res = quota_contract.response();
            AppWriter(Ok(res))
        }
        Err(e) => AppWriter(Err(e)),
    }
}

#[endpoint(tags("quota_contract"), parameters(("quota_contractname", description = "quota_contract name")))]
async fn get_quota_contract_by_name(
    quota_contractname: PathParam<String>,
) -> AppWriter<QuotaContractResponse> {
    let params = vec![WhereOptions {
        var: "name".to_string(),
        val: quota_contractname.0,
    }];
    match QuotaContractService::get_by_query(params).await {
        Ok(quota_contract) => {
            let res = quota_contract.response();
            AppWriter(Ok(res))
        }
        Err(e) => AppWriter(Err(e)),
    }
}

#[endpoint(tags("quota_contract"))]
async fn create_quota_contract(req: JsonBody<QuotaContractCreate>) -> AppWriter<String> {
    let result = QuotaContractService::create(req.0).await;
    AppWriter(result)
}

#[endpoint(tags("quota_contract"))]
async fn update_quota_contract(req: JsonBody<QuotaContractUpdate>) -> AppResult<AppWriter<String>> {
    let result = QuotaContractService::update(req.0).await;
    Ok(AppWriter(result))
}

#[endpoint(tags("quota_contract"), parameters(("id", description = "user id")))]
async fn delete_quota_contract(id: PathParam<String>) -> AppWriter<String> {
    let result = QuotaContractService::delete(id.0).await;
    AppWriter(result)
}
