use crate::{
    app_error::AppError,
    app_writer::AppResult,
    db::{Castable, CountRecord, Creatable, Database, ListOptions, Patchable, WhereOptions},
    dtos::supplier::{SupplierCreate, SupplierResponse, SupplierUpdate},
};
use anyhow::anyhow;
use serde::{Deserialize, Serialize};
use surrealdb::RecordId;

#[derive(Debug, Serialize, Deserialize)]
pub struct Supplier {
    pub id: Option<RecordId>,
    pub status: Option<String>,
    pub serial: String,
    pub name: String,
    pub address: Option<String>,
    pub website: Option<String>,
    pub image: Option<String>,
    created_at: i64,
    updated_at: i64,
}

impl Supplier {
    pub fn response(self) -> SupplierResponse {
        SupplierResponse {
            id: self.id.unwrap().to_string(),
            status: self.status,
            serial: self.serial,
            name: self.name,
            address: self.address,
            website: self.website,
            image: self.image,
            created_at: self.created_at,
            updated_at: self.updated_at,
        }
    }

    pub fn create(supplier: SupplierCreate) -> Supplier {
        Supplier {
            id: None,
            status: supplier.status,
            serial: supplier.serial,
            name: supplier.name,
            address: supplier.address,
            website: supplier.website,
            image: supplier.image,
            created_at: supplier.created_at,
            updated_at: supplier.updated_at,
        }
    }

    pub fn update(new: SupplierUpdate, old: Supplier) -> Supplier {
        Supplier {
            id: old.id,
            status: new.status,
            serial: new.serial,
            name: new.name,
            address: new.address,
            website: new.website,
            image: new.image,
            created_at: old.created_at,
            updated_at: new.updated_at,
        }
    }
}

impl Creatable for Supplier {}
impl Patchable for Supplier {}
impl Castable for Supplier {}

pub struct SupplierBmc;

impl SupplierBmc {
    const ENTITY: &'static str = "supplier";

    pub async fn get_total(params: Vec<WhereOptions>) -> AppResult<Option<CountRecord>> {
        Database::exec_query_count(Self::ENTITY, params).await
    }

    pub async fn get_list(
        page: u32,
        limit: u32,
        options: Option<ListOptions>,
        params: Vec<WhereOptions>,
    ) -> AppResult<Vec<Supplier>> {
        Database::exec_query_list(Self::ENTITY, page, limit, options, params).await
    }

    pub async fn get_by_id(id: String) -> AppResult<Option<Supplier>> {
        Database::exec_get_by_id(Self::ENTITY, &id).await
    }

    pub async fn get_by_query(params: Vec<WhereOptions>) -> AppResult<Option<Supplier>> {
        Database::exec_get_by_query(Self::ENTITY, params).await
    }

    pub async fn create(supplier: SupplierCreate) -> AppResult<String> {
        Database::exec_create(Self::ENTITY, supplier).await
    }

    pub async fn update(supplier: SupplierUpdate) -> AppResult<String> {
        let check: Option<Supplier> =
            Database::exec_get_by_id(Self::ENTITY, &supplier.id.clone()).await?;
        if check.is_none() {
            return Err(AppError::AnyHow(anyhow!("Supplier not found.")));
        }
        let old = check.unwrap();
        let tid = old.id.clone().unwrap().to_string();
        let final_tid = if tid.contains(':') {
            tid.split(':').nth(1).unwrap_or(&tid).to_string()
        } else {
            tid
        };
        let obj = Supplier::update(supplier, old);
        Database::exec_update(Self::ENTITY, &final_tid, obj).await
    }

    pub async fn delete(id: String) -> AppResult<String> {
        Database::exec_delete(Self::ENTITY, id).await
    }
}
