use std::str::FromStr;

use crate::app_error::AppError;
use crate::app_writer::AppResult;
use crate::db::{Castable, CountRecord, Creatable, Database, ListOptions, Patchable, WhereOptions};
use crate::dtos::purchase_order::{
    PurchaseOrderCreate, PurchaseOrderResponse, PurchaseOrderUpdate,
};
use anyhow::anyhow;
use chrono::Local;
use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};
use surrealdb::RecordId;

#[derive(Debug, Serialize, Deserialize)]
pub struct PurchaseOrder {
    pub id: Option<RecordId>,
    pub status: Option<String>,
    pub creator_id: Option<RecordId>,
    pub updater_id: Option<RecordId>,
    pub serial: String,
    pub contract_serial: Option<String>,
    pub purchase_time: Option<String>,
    pub pay_time: Option<String>,
    pub pay_type: Option<String>,
    pub pay_info: Option<String>,
    pub customer: Option<String>,
    pub receive_phone: Option<String>,
    pub customer_phone: Option<String>,
    pub address: Option<String>,
    pub express_type: Option<String>,
    pub express_company: Option<String>,
    pub express_order: Option<String>,
    pub platform_name: Option<String>,
    pub platform_serial: Option<String>,
    pub platform_order_serial: Option<String>,
    pub platform_fee_total: Decimal,
    pub amount: Decimal,
    pub express_fee: Decimal,
    pub total_payment: Decimal,
    created_at: i64,
    updated_at: i64,
}

impl Creatable for PurchaseOrder {}
impl Patchable for PurchaseOrder {}
impl Castable for PurchaseOrder {}

impl PurchaseOrder {
    pub async fn response(self) -> PurchaseOrderResponse {
        let creator_id = self.creator_id.map(|id| id.to_string());
        let updater_id = self.updater_id.map(|id| id.to_string());

        PurchaseOrderResponse {
            id: self.id.unwrap().to_string(),
            status: self.status,
            creator_id,
            updater_id,
            serial: self.serial,
            contract_serial: self.contract_serial,
            purchase_time: self.purchase_time,
            pay_time: self.pay_time,
            pay_type: self.pay_type,
            pay_info: self.pay_info,
            customer: self.customer,
            receive_phone: self.receive_phone,
            customer_phone: self.customer_phone,
            address: self.address,
            express_type: self.express_type,
            express_company: self.express_company,
            express_order: self.express_order,
            platform_name: self.platform_name,
            platform_serial: self.platform_serial,
            platform_order_serial: self.platform_order_serial,
            platform_fee_total: self.platform_fee_total,
            amount: self.amount,
            express_fee: self.express_fee,
            total_payment: self.total_payment,
            created_at: self.created_at,
            updated_at: self.updated_at,
        }
    }
    pub fn create(purchase_order: PurchaseOrderCreate) -> PurchaseOrder {
        let time_now = Local::now().timestamp_millis();
        let creator_id = purchase_order
            .creator_id
            .map(|id| RecordId::from_str(&id).unwrap());
        let updater_id = purchase_order
            .updater_id
            .map(|id| RecordId::from_str(&id).unwrap());

        PurchaseOrder {
            id: None,
            status: purchase_order.status,
            creator_id,
            updater_id,
            serial: purchase_order.serial,
            contract_serial: purchase_order.contract_serial,
            purchase_time: purchase_order.purchase_time,
            pay_time: purchase_order.pay_time,
            pay_type: purchase_order.pay_type,
            pay_info: purchase_order.pay_info,
            customer: purchase_order.customer,
            receive_phone: purchase_order.receive_phone,
            customer_phone: purchase_order.customer_phone,
            address: purchase_order.address,
            express_type: purchase_order.express_type,
            express_company: purchase_order.express_company,
            express_order: purchase_order.express_order,
            platform_name: purchase_order.platform_name,
            platform_serial: purchase_order.platform_serial,
            platform_order_serial: purchase_order.platform_order_serial,
            platform_fee_total: purchase_order.platform_fee_total,
            amount: purchase_order.amount,
            express_fee: purchase_order.express_fee,
            total_payment: purchase_order.total_payment,
            created_at: time_now,
            updated_at: time_now,
        }
    }

    pub fn update(new: PurchaseOrderUpdate, old: PurchaseOrder) -> PurchaseOrder {
        let time_now = Local::now().timestamp_millis();
        let creator_id = new.creator_id.map(|id| RecordId::from_str(&id).unwrap());
        let updater_id = new.updater_id.map(|id| RecordId::from_str(&id).unwrap());

        PurchaseOrder {
            id: old.id.clone(),
            status: new.status,
            creator_id,
            updater_id,
            serial: new.serial,
            contract_serial: new.contract_serial,
            purchase_time: new.purchase_time,
            pay_time: new.pay_time,
            pay_type: new.pay_type,
            pay_info: new.pay_info,
            customer: new.customer,
            receive_phone: new.receive_phone,
            customer_phone: new.customer_phone,
            address: new.address,
            express_type: new.express_type,
            express_company: new.express_company,
            express_order: new.express_order,
            platform_name: new.platform_name,
            platform_serial: new.platform_serial,
            platform_order_serial: new.platform_order_serial,
            platform_fee_total: new.platform_fee_total,
            amount: new.amount,
            express_fee: new.express_fee,
            total_payment: new.total_payment,
            created_at: old.created_at,
            updated_at: time_now,
        }
    }
}

pub struct PurchaseOrderBmc;

impl PurchaseOrderBmc {
    const ENTITY: &'static str = "purchase_order";

    pub async fn get_total(params: Vec<WhereOptions>) -> AppResult<Option<CountRecord>> {
        Database::exec_query_count(Self::ENTITY, params).await
    }

    pub async fn get_list(
        page: u32,
        limit: u32,
        options: Option<ListOptions>,
        params: Vec<WhereOptions>,
    ) -> AppResult<Vec<PurchaseOrder>> {
        Database::exec_query_list(Self::ENTITY, page, limit, options, params).await
    }

    pub async fn get_by_query(params: Vec<WhereOptions>) -> AppResult<Option<PurchaseOrder>> {
        Database::exec_get_by_query(Self::ENTITY, params).await
    }

    pub async fn get_by_id(id: String) -> AppResult<Option<PurchaseOrder>> {
        Database::exec_get_by_id(Self::ENTITY, &id).await
    }

    pub async fn create(purchase_order: PurchaseOrderCreate) -> AppResult<String> {
        let obj = PurchaseOrder::create(purchase_order);
        Database::exec_create(Self::ENTITY, obj).await
    }

    pub async fn update(purchase_order: PurchaseOrderUpdate) -> AppResult<String> {
        let check: Option<PurchaseOrder> =
            Database::exec_get_by_id(Self::ENTITY, &purchase_order.id.clone()).await?;
        if check.is_none() {
            return Err(AppError::AnyHow(anyhow!("PurchaseOrder not found.")));
        }
        let old = check.unwrap();
        let tid = old.id.clone().unwrap().to_string();
        let final_tid = if tid.contains(':') {
            tid.split(':').nth(1).unwrap_or(&tid).to_string()
        } else {
            tid
        };
        let obj = PurchaseOrder::update(purchase_order, old);
        Database::exec_update(Self::ENTITY, &final_tid, obj).await
    }

    pub async fn delete(id: String) -> AppResult<String> {
        Database::exec_delete(Self::ENTITY, id).await
    }
}
