use std::str::FromStr;

use crate::app_error::AppError;
use crate::app_writer::AppResult;
use crate::db::{Castable, CountRecord, Creatable, Database, ListOptions, Patchable, WhereOptions};
use crate::dtos::purchase_order_info::{
    PurchaseOrderInfoCreate, PurchaseOrderInfoResponse, PurchaseOrderInfoUpdate,
};
use anyhow::anyhow;
use chrono::Local;
use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};
use surrealdb::RecordId;

#[derive(Debug, Serialize, Deserialize)]
pub struct PurchaseOrderInfo {
    pub id: Option<RecordId>,
    pub order_serial: String,
    pub product_serial: String,
    pub product_name: Option<String>,
    pub product_model: Option<String>,
    pub product_type: Option<String>,
    pub price: Decimal,
    pub platform_fee: Decimal,
    pub discount: Decimal,
    pub quantity: Decimal,
    pub amount: Decimal,
    pub express_company: Option<String>,
    pub express_order: Option<String>,
    pub system_remark: Option<String>,
    created_at: i64,
    updated_at: i64,
}

impl Creatable for PurchaseOrderInfo {}
impl Patchable for PurchaseOrderInfo {}
impl Castable for PurchaseOrderInfo {}

impl PurchaseOrderInfo {
    pub async fn response(self) -> PurchaseOrderInfoResponse {
        PurchaseOrderInfoResponse {
            id: self.id.unwrap().to_string(),
            order_serial: self.order_serial,
            product_serial: self.product_serial,
            product_name: self.product_name,
            product_model: self.product_model,
            product_type: self.product_type,
            price: self.price,
            platform_fee: self.platform_fee,
            discount: self.discount,
            quantity: self.quantity,
            amount: self.amount,
            express_company: self.express_company,
            express_order: self.express_order,
            system_remark: self.system_remark,
            created_at: self.created_at,
            updated_at: self.updated_at,
        }
    }
    pub fn create(purchase_order: PurchaseOrderInfoCreate) -> PurchaseOrderInfo {
        let time_now = Local::now().timestamp_millis();

        PurchaseOrderInfo {
            id: None,
            order_serial: purchase_order.order_serial,
            product_serial: purchase_order.product_serial,
            product_name: purchase_order.product_name,
            product_model: purchase_order.product_model,
            product_type: purchase_order.product_type,
            price: purchase_order.price,
            platform_fee: purchase_order.platform_fee,
            discount: purchase_order.discount,
            quantity: purchase_order.quantity,
            amount: purchase_order.amount,
            express_company: purchase_order.express_company,
            express_order: purchase_order.express_order,
            system_remark: purchase_order.system_remark,
            created_at: time_now,
            updated_at: time_now,
        }
    }

    pub fn update(new: PurchaseOrderInfoUpdate, old: PurchaseOrderInfo) -> PurchaseOrderInfo {
        let time_now = Local::now().timestamp_millis();

        PurchaseOrderInfo {
            id: old.id.clone(),
            order_serial: new.order_serial,
            product_serial: new.product_serial,
            product_name: new.product_name,
            product_model: new.product_model,
            product_type: new.product_type,
            price: new.price,
            platform_fee: new.platform_fee,
            discount: new.discount,
            quantity: new.quantity,
            amount: new.amount,
            express_company: new.express_company,
            express_order: new.express_order,
            system_remark: new.system_remark,
            created_at: old.created_at,
            updated_at: time_now,
        }
    }
}

pub struct PurchaseOrderInfoBmc;

impl PurchaseOrderInfoBmc {
    const ENTITY: &'static str = "purchase_order_info";

    pub async fn get_total(params: Vec<WhereOptions>) -> AppResult<Option<CountRecord>> {
        Database::exec_query_count(Self::ENTITY, params).await
    }

    pub async fn get_list(
        page: u32,
        limit: u32,
        options: Option<ListOptions>,
        params: Vec<WhereOptions>,
    ) -> AppResult<Vec<PurchaseOrderInfo>> {
        Database::exec_query_list(Self::ENTITY, page, limit, options, params).await
    }

    pub async fn get_by_query(params: Vec<WhereOptions>) -> AppResult<Option<PurchaseOrderInfo>> {
        Database::exec_get_by_query(Self::ENTITY, params).await
    }

    pub async fn get_by_id(id: String) -> AppResult<Option<PurchaseOrderInfo>> {
        Database::exec_get_by_id(Self::ENTITY, &id).await
    }

    pub async fn create(purchase_order: PurchaseOrderInfoCreate) -> AppResult<String> {
        let obj = PurchaseOrderInfo::create(purchase_order);
        Database::exec_create(Self::ENTITY, obj).await
    }

    pub async fn update(purchase_order: PurchaseOrderInfoUpdate) -> AppResult<String> {
        let check: Option<PurchaseOrderInfo> =
            Database::exec_get_by_id(Self::ENTITY, &purchase_order.id.clone()).await?;
        if check.is_none() {
            return Err(AppError::AnyHow(anyhow!("PurchaseOrderInfo not found.")));
        }
        let old = check.unwrap();
        let tid = old.id.clone().unwrap().to_string();
        let final_tid = if tid.contains(':') {
            tid.split(':').nth(1).unwrap_or(&tid).to_string()
        } else {
            tid
        };
        let obj = PurchaseOrderInfo::update(purchase_order, old);
        Database::exec_update(Self::ENTITY, &final_tid, obj).await
    }

    pub async fn delete(id: String) -> AppResult<String> {
        Database::exec_delete(Self::ENTITY, id).await
    }
}
