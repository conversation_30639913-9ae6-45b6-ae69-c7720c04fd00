<template>
    <div class="q-pa-sm">
      <q-card v-if="itemDetail?.id">
        <q-card-section>
          <div class="row q-my-md">
            <div class="text-h5 col" style="margin-bottom: 10px">
              合同详情:
              {{ itemDetail.name }}
              <q-chip
                square
                outline
                text-color="white"
                :color="getStatusColor(itemDetail.status)"
              >
                {{ getStatusText(itemDetail.status) }}
              </q-chip>
            </div>
            <div class="col-auto">
              <q-btn-group push>
                <q-btn
                  size="sm"
                  push
                  label="修改合同信息"
                  icon="edit"
                  color="secondary"
                  @click="editContractDetail"
                />
                <q-btn
                  size="sm"
                  push
                  label="查看附件"
                  icon="attachment"
                  color="orange"
                  @click="showAttachment"
                />
                <q-btn
                  size="sm"
                  push
                  label="打印合同"
                  icon="print"
                  color="primary"
                  @click="printContract"
                />
              </q-btn-group>
            </div>
          </div>

          <!-- 基本信息 -->
          <div class="text-h6 q-mb-md">基本信息</div>
          <div class="row q-my-md">
            <div class="col">
              <div class="row">
                <div class="col-4 row-title">合同编号</div>
                <div class="col-8">
                  {{ itemDetail.serial }}
                </div>
              </div>
            </div>
            <div class="col">
              <div class="row">
                <div class="col-4 row-title">合同类型</div>
                <div class="col-8">
                  {{ itemDetail.contract_type }}
                </div>
              </div>
            </div>
          </div>
          <div class="row q-my-md">
            <div class="col">
              <div class="row">
                <div class="col-4 row-title">申请人</div>
                <div class="col-8">
                  {{ itemDetail.applier }}
                </div>
              </div>
            </div>
            <div class="col">
              <div class="row">
                <div class="col-4 row-title">资金方</div>
                <div class="col-8">
                  {{ itemDetail.funder }}
                </div>
              </div>
            </div>
          </div>
          <div class="row q-my-md">
            <div class="col">
              <div class="row">
                <div class="col-4 row-title">供应链金融公司</div>
                <div class="col-8">
                  {{ itemDetail.scf_company }}
                </div>
              </div>
            </div>
            <div class="col">
              <div class="row">
                <div class="col-4 row-title">签约时间</div>
                <div class="col-8">
                  {{ itemDetail.sign_time }}
                </div>
              </div>
            </div>
          </div>
          <div class="row q-my-md">
            <div class="col">
              <div class="row">
                <div class="col-4 row-title">合同开始时间</div>
                <div class="col-8">
                  {{ itemDetail.begin_time }}
                </div>
              </div>
            </div>
            <div class="col">
              <div class="row">
                <div class="col-4 row-title">合同结束时间</div>
                <div class="col-8">
                  {{ itemDetail.end_time }}
                </div>
              </div>
            </div>
          </div>

          <!-- 财务信息 -->
          <div class="text-h6 q-mb-md q-mt-lg">财务信息</div>
          <div class="row q-my-md">
            <div class="col">
              <div class="row">
                <div class="col-4 row-title">服务费率</div>
                <div class="col-8">{{ itemDetail.service_fee }}%</div>
              </div>
            </div>
            <div class="col">
              <div class="row">
                <div class="col-4 row-title">申请额度</div>
                <div class="col-8">
                  ¥{{ formatAmount(itemDetail.application_quota) }}
                </div>
              </div>
            </div>
          </div>
          <div class="row q-my-md">
            <div class="col">
              <div class="row">
                <div class="col-4 row-title">确认额度</div>
                <div class="col-8">
                  ¥{{ formatAmount(itemDetail.confirm_quota) }}
                </div>
              </div>
            </div>
            <div class="col">
              <div class="row">
                <div class="col-4 row-title">已用额度</div>
                <div class="col-8">
                  ¥{{ formatAmount(itemDetail.used_quota) }}
                </div>
              </div>
            </div>
          </div>
          <div class="row q-my-md">
            <div class="col">
              <div class="row">
                <div class="col-4 row-title">利润计算方式</div>
                <div class="col-8">
                  {{ itemDetail.profit_calc_method }}
                </div>
              </div>
            </div>
            <div class="col">
              <div class="row">
                <div class="col-4 row-title">计算周期</div>
                <div class="col-8">
                  {{ itemDetail.profit_calc_period }}
                </div>
              </div>
            </div>
          </div>
          <div class="row q-my-md">
            <div class="col">
              <div class="row">
                <div class="col-4 row-title">违约金计算方式</div>
                <div class="col-8">
                  {{ itemDetail.penalty_calc_period }}
                </div>
              </div>
            </div>
            <div class="col">
              <div class="row">
                <div class="col-4 row-title">违约金计算周期</div>
                <div class="col-8">
                  {{ itemDetail.penalty_calc_period }}
                </div>
              </div>
            </div>
          </div>

          <!-- 相关方信息 -->
          <div class="text-h6 q-mb-md q-mt-lg">相关方信息</div>
          <div class="row q-my-md">
            <div class="col">
              <div class="row">
                <div class="col-4 row-title">供应商</div>
                <div class="col-8">
                  {{ itemDetail.supplier_name || "未指定" }}
                </div>
              </div>
            </div>
            <div class="col">
              <div class="row">
                <div class="col-4 row-title">中标方</div>
                <div class="col-8">
                  {{ itemDetail.bid_winner_name || "未指定" }}
                </div>
              </div>
            </div>
          </div>
          <div class="row q-my-md">
            <div class="col">
              <div class="row">
                <div class="col-4 row-title">招标方</div>
                <div class="col-8">
                  {{ itemDetail.bid_owner_name || "未指定" }}
                </div>
              </div>
            </div>
            <div class="col">
              <div class="row">
                <div class="col-4 row-title">仓库</div>
                <div class="col-8">
                  {{ itemDetail.warehouse_name || "未指定" }}
                </div>
              </div>
            </div>
          </div>

          <!-- 产品类别 -->
          <div class="row q-my-md">
            <div class="col">
              <div class="row">
                <div class="col-4 row-title">产品类别</div>
                <div class="col-8">
                  <q-chip
                    v-for="category in itemDetail.product_category"
                    :key="category"
                    color="primary"
                    text-color="white"
                    size="sm"
                    class="q-mr-sm"
                  >
                    {{ category }}
                  </q-chip>
                </div>
              </div>
            </div>
          </div>

          <!-- 系统信息 -->
          <div class="text-h6 q-mb-md q-mt-lg">系统信息</div>
          <div class="row q-my-md">
            <div class="col">
              <div class="row">
                <div class="col-4 row-title">创建时间</div>
                <div class="col-8">
                  {{ showDateTime(itemDetail.created_at) }}
                </div>
              </div>
            </div>
            <div class="col">
              <div class="row">
                <div class="col-4 row-title">更新时间</div>
                <div class="col-8">
                  {{ showDateTime(itemDetail.updated_at) }}
                </div>
              </div>
            </div>
          </div>
          <div class="row q-my-md">
            <div class="col">
              <div class="row">
                <div class="col-4 row-title">创建人</div>
                <div class="col-8">
                  {{ itemDetail.created_by }}
                </div>
              </div>
            </div>
            <div class="col">
              <div class="row">
                <div class="col-4 row-title">是否删除</div>
                <div class="col-8">
                  <q-chip
                    :color="itemDetail.is_delete ? 'negative' : 'positive'"
                    text-color="white"
                    size="sm"
                  >
                    {{ itemDetail.is_delete ? "已删除" : "正常" }}
                  </q-chip>
                </div>
              </div>
            </div>
          </div>
        </q-card-section>
      </q-card>

      <q-card class="q-my-md">
        <q-card-section>
          <div class="text-h6">合同描述</div>
        </q-card-section>
        <q-card-section>
          <div v-if="itemDetail?.desc" class="contract-desc">
            {{ itemDetail.desc }}
          </div>
          <span v-else> <q-icon name="warning" />暂无描述信息</span>
        </q-card-section>
      </q-card>
    </div>
</template>

<script setup>
import { Notify } from "quasar";
import { getActionByPath } from "src/api/manage";
import { FormatTimeStamp } from "src/utils/date";
import { computed, onMounted, ref } from "vue";

// Props定义
const props = defineProps({
  itemId: {
    type: String,
    required: true
  },
  itemDetail: {
    type: Object,
    required: true
  }
});

const url = {
  item: "/api/financial_contract",
  delete: "/api/financial_contract",
};

const itemDetail = ref();

// 状态字典
const statusDict = [
  { value: "draft", label: "草稿", color: "grey" },
  { value: "new", label: "新建", color: "blue" },
  { value: "processing", label: "处理中", color: "orange" },
  { value: "done", label: "已完成", color: "green" },
  { value: "trash", label: "已废弃", color: "red" },
  { value: "expired", label: "已过期", color: "red" },
];

onMounted(async () => {
  if (props.itemId) {
    itemDetail.value = props.itemDetail;
  } else {
    itemDetail.value = {};
    Notify.create({
      type: "warning",
      message: "信息查询失败，请重试",
      position: "top-right",
    });
  }
});

const showDateTime = computed(() => {
  return (datetime) => {
    return FormatTimeStamp(datetime);
  };
});

// 获取状态颜色
const getStatusColor = (status) => {
  const statusItem = statusDict.find((item) => item.value === status);
  return statusItem ? statusItem.color : "grey";
};

// 获取状态文本
const getStatusText = (status) => {
  const statusItem = statusDict.find((item) => item.value === status);
  return statusItem ? statusItem.label : status;
};

// 格式化金额
const formatAmount = (amount) => {
  if (!amount) return "0";
  return Number(amount).toLocaleString();
};

// 编辑合同详情
const editContractDetail = () => {
  router.push({
    path: "/contract/create",
    query: { id: itemDetail.value.id },
  });
};

// 显示附件
const showAttachment = () => {
  Notify.create({
    type: "info",
    message: "附件功能开发中",
    position: "top-right",
  });
};

// 打印合同
const printContract = () => {
  window.print();
};

const handleDetail = async (id) => {
  const { code, data } = await getActionByPath(url.item, [id]);
  if (code === 200) {
    itemDetail.value = data;
    Notify.create({
      type: "positive",
      message: "信息查询成功",
      position: "top-right",
    });
  } else {
    itemDetail.value = {};
    Notify.create({
      type: "warning",
      message: "信息查询失败，请重试",
      position: "top-right",
    });
  }
};
</script>

<style scoped lang="scss">
.row-title {
  font-weight: 600;
  color: #666;
  padding: 8px 0;
}

.contract-desc {
  font-size: 14px;
  line-height: 1.6;
  color: #333;
  background: #f5f5f5;
  padding: 16px;
  border-radius: 4px;
}

.text-h6 {
  color: #1976d2;
  border-bottom: 2px solid #e0e0e0;
  padding-bottom: 8px;
}

:deep(img) {
  max-width: 100%;
}

@media print {
  .q-btn-group {
    display: none;
  }
}
</style>
