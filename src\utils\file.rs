use anyhow::anyhow;
use salvo::http::form::FilePart;
use std::path::{Path, PathBuf};
use tokio::fs;
// 引入 rand_utils 模块
use crate::{app_writer::AppResult, utils::rand_utils::random_string};

/// 处理上传的文件，校验文件类型并保存到指定路径
///
/// # 参数
/// - `file_part`: 要处理的文件部分
/// - `save_path`: 文件保存的目标路径
///
/// # 返回值
/// - 如果文件类型允许且保存成功，返回包含完整保存路径的 `Ok` 结果
/// - 如果文件类型不允许或保存失败，返回包含错误信息的 `Err` 结果
pub async fn handle_uploaded_file(file_part: &FilePart, save_path: &str) -> AppResult<String> {
    // 允许的文件扩展名
    let allowed_extensions = vec![
        "xlsx", "xls", "pdf", "docx", "doc", "jpg", "jpeg", "png", "gif", "csv", "txt",
    ];
    // 获取文件的扩展名
    let file_ext = Path::new(file_part.name().unwrap_or("upload_tmp"))
        .extension()
        .and_then(std::ffi::OsStr::to_str);

    if let Some(ext) = file_ext {
        if allowed_extensions.contains(&ext) {
            // 获取文件名主干（不含扩展名）
            let file_name = file_part.name().unwrap_or("upload_tmp");
            let file_stem = Path::new(file_name)
                .file_stem() // 获取不含扩展名的主干部分
                .and_then(|s| s.to_str())
                .unwrap_or(file_name);

            // 生成 6 位随机编码
            let random_code = random_string(6);

            // 组合新文件名（自动处理扩展名点符号）
            let new_file_name = format!("{}_{}.{}", file_stem, random_code, ext);

            // 组合保存路径和新文件名
            let full_save_path = PathBuf::from(save_path).join(new_file_name);

            // 自动创建缺失的目录 (新增代码)
            if let Some(parent_dir) = full_save_path.parent() {
                fs::create_dir_all(parent_dir)
                    .await
                    .map_err(|e| anyhow!("目录创建失败: {}", e))?;
            }

            // 保存文件到指定路径
            if let Err(e) = fs::copy(file_part.path(), full_save_path.clone()).await {
                return Err(anyhow!("文件保存失败: {}", e.to_string()).into());
            }
            // 返回完整保存路径的字符串表示
            return Ok(full_save_path.to_string_lossy().to_string());
        }
    }
    Err(anyhow!("不允许的文件类型，请上传 Excel、PDF、Word 或图片文档").into())
}
