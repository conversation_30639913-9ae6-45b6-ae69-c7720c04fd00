use std::str::FromStr;

use crate::{
    app_writer::AppResult,
    db::{
        Castable, CountRecord, Creatable, Database, ListOptions, Patchable, RelateParams,
        UpdateOptions, WhereOptions,
    },
    dtos::repayment::{RepaymentCreate, RepaymentResponse, RepaymentUpdate},
    entities::financial_contract::CalcPeriod,
    utils::rand_utils::random_uppercase_serial,
};
use anyhow::anyhow;
use chrono::{Datelike, Local, NaiveDate};
use rust_decimal::{prelude::*, Decimal};
use serde::{Deserialize, Serialize};
use surrealdb::RecordId;

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Repayment {
    pub id: Option<RecordId>,
    pub serial: Option<String>,            // 还款计划编号
    pub contract_id: Option<RecordId>,     // 合同ID
    pub profit_predict: Option<String>,    // 需要偿还的利润
    pub principal_predict: Option<String>, // 需要偿还的本金（从合同获取的额度）
    pub amount_predict: Option<String>,    // 需要偿还的总额
    pub profit_actual: Option<String>,     // 实际偿还的利润
    pub principal_actual: Option<String>,  // 实际偿还的本金（从合同获取的额度）
    pub amount_actual: Option<String>,     // 实际偿还的总额
    pub profit_remain: Option<String>,     // 剩余需要偿还的利润
    pub principal_remain: Option<String>,  // 剩余需要偿还的本金（从合同获取的额度）
    pub amount_remain: Option<String>,     // 剩余需要偿还的总额
    pub target_amount: Option<String>,     // 目标费用总额（订单总金额）

    pub profit_calc_fee: Decimal,                // 利润计算费用
    pub penalty_calc_fee: Decimal,               // 违约金计算费用
    pub profit_calc_period: Option<CalcPeriod>,  // 计算周期
    pub penalty_calc_period: Option<CalcPeriod>, // 违约金计算方式
    pub update_time: Option<i64>,                // 利息更新时间

    pub begin_date: Option<String>, // 额度支取的第一天，从这一天开始计算利润
    pub end_date: Option<String>,   // 约定偿还额度的最后一天，过了这一天就计算违约金
    pub repay_date: Option<String>, // 实际还款日期

    pub remark: Option<String>,     // 备注
    pub status: Option<String>,     // 状态
    pub creator_id: Option<String>, // 创建人ID
    pub updater_id: Option<String>, // 更新人ID
    pub created_at: i64,
    pub updated_at: i64,
}

impl Creatable for Repayment {}
impl Patchable for Repayment {}
impl Castable for Repayment {}

impl Repayment {
    /// 将 Repayment 实体转换为响应 DTO
    pub fn response(self) -> RepaymentResponse {
        RepaymentResponse {
            id: self.id.unwrap().to_string(),
            serial: self.serial,
            contract_id: self.contract_id.map(|id| id.to_string()),
            profit_predict: self.profit_predict,
            principal_predict: self.principal_predict,
            amount_predict: self.amount_predict,
            profit_actual: self.profit_actual,
            principal_actual: self.principal_actual,
            amount_actual: self.amount_actual,
            profit_remain: self.profit_remain,
            principal_remain: self.principal_remain,
            amount_remain: self.amount_remain,
            target_amount: self.target_amount,
            profit_calc_fee: self.profit_calc_fee,
            penalty_calc_fee: self.penalty_calc_fee,
            profit_calc_period: self.profit_calc_period.map(|p| format!("{:?}", p)),
            penalty_calc_period: self.penalty_calc_period.map(|p| format!("{:?}", p)),
            update_time: self.update_time,
            begin_date: self.begin_date,
            end_date: self.end_date,
            repay_date: self.repay_date,
            remark: self.remark,
            status: self.status,
            creator_id: self.creator_id,
            updater_id: self.updater_id,
            created_at: self.created_at,
            updated_at: self.updated_at,
        }
    }

    /// 从创建 DTO 创建新的 Repayment 实体
    pub fn create(obj: RepaymentCreate) -> Repayment {
        let time_now: i64 = Local::now().timestamp_millis();
        let serial = obj
            .serial
            .unwrap_or_else(|| random_uppercase_serial(Some("RP".to_string()), 6));
        let contract_id = if obj.contract_id.is_some() {
            Some(RecordId::from_str(&obj.contract_id.unwrap()).unwrap())
        } else {
            None
        };
        Repayment {
            id: None,
            serial: Some(serial),
            contract_id: contract_id,
            profit_predict: obj.profit_predict,
            principal_predict: obj.principal_predict,
            amount_predict: obj.amount_predict,
            profit_actual: obj.profit_actual,
            principal_actual: obj.principal_actual,
            amount_actual: obj.amount_actual,
            profit_remain: obj.profit_remain,
            principal_remain: obj.principal_remain,
            amount_remain: obj.amount_remain,
            target_amount: obj.target_amount,
            profit_calc_fee: obj.profit_calc_fee,
            penalty_calc_fee: obj.penalty_calc_fee,
            profit_calc_period: obj
                .profit_calc_period
                .and_then(|p| CalcPeriod::from_str(&p)),
            penalty_calc_period: obj
                .penalty_calc_period
                .and_then(|p| CalcPeriod::from_str(&p)),
            update_time: None,
            begin_date: obj.begin_date,
            end_date: obj.end_date,
            repay_date: obj.repay_date,
            remark: obj.remark,
            status: obj.status, // 状态
            creator_id: obj.creator_id,
            updater_id: None, // 创建时不设置更新人ID
            created_at: time_now,
            updated_at: time_now,
        }
    }

    /// 从更新 DTO 更新 Repayment 实体
    pub fn update(mut self, obj: RepaymentUpdate) -> Repayment {
        let time_now: i64 = Local::now().timestamp_millis();

        // 只更新提供的字段
        if let Some(serial) = obj.serial {
            self.serial = Some(serial);
        }
        if let Some(contract_id) = obj.contract_id {
            self.contract_id =
                RecordId::from_str(&format!("financial_contract:{}", contract_id)).ok();
        }
        if let Some(profit_predict) = obj.profit_predict {
            self.profit_predict = Some(profit_predict);
        }
        if let Some(principal_predict) = obj.principal_predict {
            self.principal_predict = Some(principal_predict);
        }
        if let Some(amount_predict) = obj.amount_predict {
            self.amount_predict = Some(amount_predict);
        }
        if let Some(profit_actual) = obj.profit_actual {
            self.profit_actual = Some(profit_actual);
        }
        if let Some(principal_actual) = obj.principal_actual {
            self.principal_actual = Some(principal_actual);
        }
        if let Some(amount_actual) = obj.amount_actual {
            self.amount_actual = Some(amount_actual);
        }
        if let Some(profit_remain) = obj.profit_remain {
            self.profit_remain = Some(profit_remain);
        }
        if let Some(principal_remain) = obj.principal_remain {
            self.principal_remain = Some(principal_remain);
        }
        if let Some(amount_remain) = obj.amount_remain {
            self.amount_remain = Some(amount_remain);
        }
        if let Some(target_amount) = obj.target_amount {
            self.target_amount = Some(target_amount);
        }
        if let Some(profit_calc_fee) = obj.profit_calc_fee {
            self.profit_calc_fee = profit_calc_fee;
        }
        if let Some(penalty_calc_fee) = obj.penalty_calc_fee {
            self.penalty_calc_fee = penalty_calc_fee;
        }
        if let Some(profit_calc_period) = obj.profit_calc_period {
            self.profit_calc_period = CalcPeriod::from_str(&profit_calc_period);
        }
        if let Some(penalty_calc_period) = obj.penalty_calc_period {
            self.penalty_calc_period = CalcPeriod::from_str(&penalty_calc_period);
        }
        if let Some(begin_date) = obj.begin_date {
            self.begin_date = Some(begin_date);
        }
        if let Some(end_date) = obj.end_date {
            self.end_date = Some(end_date);
        }
        if let Some(repay_date) = obj.repay_date {
            self.repay_date = Some(repay_date);
        }
        if let Some(remark) = obj.remark {
            self.remark = Some(remark);
        }
        if let Some(status) = obj.status {
            self.status = Some(status);
        }
        if let Some(updater_id) = obj.updater_id {
            self.updater_id = Some(updater_id);
        }
        if let Some(update_time) = obj.update_time {
            self.update_time = Some(update_time);
        }

        // 更新时间戳
        self.updated_at = time_now;

        self
    }
}

pub struct RepaymentBmc;

impl RepaymentBmc {
    const ENTITY: &'static str = "repayment";

    pub async fn get_list(
        page: u32,
        limit: u32,
        options: Option<ListOptions>,
        params: Vec<WhereOptions>,
    ) -> AppResult<Vec<Repayment>> {
        Database::exec_query_list(Self::ENTITY, page, limit, options, params).await
    }

    pub async fn get_total(params: Vec<WhereOptions>) -> AppResult<Option<CountRecord>> {
        Database::exec_query_count(Self::ENTITY, params).await
    }

    pub async fn get_by_query(params: Vec<WhereOptions>) -> AppResult<Option<Repayment>> {
        Database::exec_get_by_query(Self::ENTITY, params).await
    }

    /// 根据ID获取还款记录（自动执行期间计算）
    ///
    /// # 参数
    /// * `id` - 还款记录ID
    ///
    /// # 返回值
    /// * `AppResult<Option<Repayment>>` - 操作结果，返回还款记录或None
    ///
    /// # 功能
    /// - 获取还款记录后，自动判断状态
    /// - 如果状态为 "processing"、"pending" 或 "partial"，自动执行期间计算
    /// - 将计算结果更新到数据库并返回更新后的记录
    /// - 其他状态直接返回原记录
    pub async fn get_by_id(id: &str) -> AppResult<Option<Repayment>> {
        // 在函数开始时获取统一的时间戳，避免执行过程中时间切换
        let execution_timestamp = Local::now().timestamp_millis();
        let execution_date = chrono::DateTime::from_timestamp_millis(execution_timestamp)
            .ok_or_else(|| anyhow!("Invalid execution timestamp: {}", execution_timestamp))?
            .date_naive();

        // 先获取原始记录
        let repayment_opt: Option<Repayment> = Database::exec_get_by_id(Self::ENTITY, id).await?;
        if let Some(mut repayment) = repayment_opt {
            // 检查状态是否需要执行期间计算
            if let Some(ref status) = repayment.status {
                if status == "processing" || status == "pending" || status == "partial" {
                    // 执行期间计算，传入统一的时间戳和日期
                    match Self::period_calc_internal_with_time(
                        &repayment,
                        execution_date,
                        execution_timestamp,
                    )
                    .await
                    {
                        Ok((profit_count, penalty_count, total_amount)) => {
                            // 累加计算结果到现有记录（增量更新）

                            // 1. 更新 profit_predict = 原值 + 新增利润
                            let original_profit_predict =
                                if let Some(profit_str) = &repayment.profit_predict {
                                    Decimal::from_str(profit_str).unwrap_or(Decimal::ZERO)
                                } else {
                                    Decimal::ZERO
                                };
                            let new_profit_predict = original_profit_predict + profit_count;
                            repayment.profit_predict = Some(new_profit_predict.to_string());

                            // 2. 更新 amount_predict = 原值 + 新增利润 + 新增违约金
                            let original_amount_predict =
                                if let Some(amount_str) = &repayment.amount_predict {
                                    Decimal::from_str(amount_str).unwrap_or(Decimal::ZERO)
                                } else {
                                    Decimal::ZERO
                                };
                            let new_amount_predict =
                                original_amount_predict + profit_count + penalty_count;
                            repayment.amount_predict = Some(new_amount_predict.to_string());

                            // 3. 更新 profit_remain = 原值 + 新增利润
                            let original_profit_remain =
                                if let Some(profit_remain_str) = &repayment.profit_remain {
                                    Decimal::from_str(profit_remain_str).unwrap_or(Decimal::ZERO)
                                } else {
                                    Decimal::ZERO
                                };
                            let new_profit_remain = original_profit_remain + profit_count;
                            repayment.profit_remain = Some(new_profit_remain.to_string());

                            // 4. 更新 amount_remain = 原值 + 新增利润 + 新增违约金
                            repayment.amount_remain = Some(total_amount.to_string());

                            // 使用统一的时间戳更新，避免时间切换问题
                            repayment.updated_at = execution_timestamp;
                            repayment.update_time = Some(execution_timestamp);

                            // 获取记录ID用于更新
                            if let Some(record_id) = &repayment.id {
                                let tid = record_id.to_string();
                                let final_tid = if tid.contains(':') {
                                    tid.split(':').nth(1).unwrap_or(&tid).to_string()
                                } else {
                                    tid.clone()
                                };

                                // 执行数据库更新
                                match Database::exec_update(
                                    Self::ENTITY,
                                    &final_tid,
                                    repayment.clone(),
                                )
                                .await
                                {
                                    Ok(_) => {
                                        // 更新成功，返回更新后的记录
                                        return Ok(Some(repayment));
                                    }
                                    Err(e) => {
                                        // 更新失败，记录错误但仍返回计算后的记录
                                        eprintln!("Failed to update repayment after period calculation: {}", e);
                                        return Ok(Some(repayment));
                                    }
                                }
                            }
                        }
                        Err(e) => {
                            // 计算失败，记录错误但仍返回原记录
                            eprintln!("Failed to calculate period for repayment {}: {}", id, e);
                        }
                    }
                }
            }

            // 状态不需要计算或计算失败，返回原记录
            Ok(Some(repayment))
        } else {
            // 记录不存在
            Ok(None)
        }
    }

    pub async fn create(repayment: RepaymentCreate) -> AppResult<String> {
        let obj = Repayment::create(repayment);
        Database::exec_create(Self::ENTITY, obj).await
    }

    pub async fn update(repayment: RepaymentUpdate) -> AppResult<String> {
        let check: Option<Repayment> =
            Database::exec_get_by_id(Self::ENTITY, &repayment.id.clone().to_string()).await?;
        if check.is_none() {
            return Err(anyhow!("Repayment not found.").into());
        }
        let old = check.unwrap();
        let tid = old.id.clone().unwrap().to_string();
        let final_tid = if tid.contains(':') {
            tid.split(':').nth(1).unwrap_or(&tid).to_string()
        } else {
            tid.clone()
        };
        let obj = Repayment::update(old, repayment);
        Database::exec_update(Self::ENTITY, &final_tid, obj).await
    }

    pub async fn delete(id: String) -> AppResult<String> {
        let check: Option<Repayment> = Database::exec_get_by_id(Self::ENTITY, &id).await?;
        if check.is_none() {
            return Err(anyhow!("Repayment not found.").into());
        }
        let check = check.unwrap();
        // 检查还款计划状态，只有草稿或新建状态才能删除
        if let Some(status) = &check.status {
            if status != "draft" && status != "new" {
                return Err(anyhow!("Cannot delete completed repayment.").into());
            }
        }
        if check.status.as_deref() != Some("draft") || check.status.as_deref() != Some("new") {
            return Err(anyhow!("Cannot delete completed repayment.").into());
        }
        // 删除还款计划前检查状态
        Database::exec_delete(Self::ENTITY, id).await
    }

    pub async fn relate_to_item(params: RelateParams, state: &str) -> AppResult<String> {
        let from = vec![params.from];
        Database::exec_relate_batch(state, from, params.to).await
    }

    /// 根据查询条件更新单个字段
    ///
    /// # 参数
    /// * `params` - 查询条件
    /// * `update_field` - 要更新的字段名
    /// * `update_value` - 要更新的字段值
    ///
    /// # 返回值
    /// * `AppResult<String>` - 操作结果，返回更新记录的ID
    ///
    /// # 示例
    /// ```rust
    /// let params = vec![WhereOptions::new("serial".to_string(), "RP001".to_string())];
    /// let result = RepaymentBmc::update_field(params, "status", "completed").await?;
    /// ```
    pub async fn update_field(
        params: Vec<WhereOptions>,
        update_field: &str,
        update_value: &str,
    ) -> AppResult<String> {
        Database::exec_update_by_query(Self::ENTITY, params, update_field, update_value).await
    }

    /// 根据查询条件更新多个字段
    ///
    /// # 参数
    /// * `params` - 查询条件
    /// * `update_fields` - 要更新的字段列表
    ///
    /// # 返回值
    /// * `AppResult<String>` - 操作结果，返回更新记录的ID
    ///
    /// # 示例
    /// ```rust
    /// let params = vec![WhereOptions::new("serial".to_string(), "SO001".to_string())];
    /// let update_fields = vec![
    ///     UpdateOptions::new("status".to_string(), "completed".to_string()),
    ///     UpdateOptions::new("amount".to_string(), "1000.00".to_string()),
    /// ];
    /// let result = SalesOrderBmc::update_multiple_fields(params, update_fields).await?;
    /// ```
    pub async fn update_multiple_fields(
        params: Vec<WhereOptions>,
        update_fields: Vec<UpdateOptions>,
    ) -> AppResult<String> {
        Database::exec_update_multiple_fields_by_query(Self::ENTITY, params, update_fields).await
    }

    /// 计算还款计划的利润、违约金等信息
    ///
    /// # 参数
    /// * `repayment_id` - 还款计划ID
    ///
    /// # 返回值
    /// * `AppResult<(Decimal, Decimal, Decimal)>` - 返回 (利润金额, 违约金金额, 总应还金额)
    ///
    /// # 计算规则
    /// 1. 利润计算：
    ///    - profit_calc_fee 是年化利率
    ///    - 根据 CalcPeriod 决定计算方式：
    ///      - DAY: 按日利率计算 (年化利率/365)
    ///      - MONTH: 按月计算，当前日期与 begin_date 差值满足月份才计算
    ///      - YEAR: 按年计算，当前日期与 begin_date 差值满足年份才计算
    ///      - QUARTER: 按季度计算，当前日期与 begin_date 差值满足季度才计算
    ///      - ONCE: 不自动计算，使用录入的数据
    /// 2. 违约金计算：
    ///    - penalty_calc_fee 是日息利率
    ///    - 按当前日期与约定还款日期(end_date)的差值，按日息计算
    /// 3. 计算基数：principal_remain (剩余需要偿还的本金)
    ///
    /// # 示例
    /// ```rust
    /// let (profit, penalty, total) = RepaymentBmc::period_calc("repayment:123").await?;
    /// println!("利润: {}, 违约金: {}, 总计: {}", profit, penalty, total);
    /// ```
    pub async fn period_calc(repayment_id: &str) -> AppResult<(Decimal, Decimal, Decimal)> {
        // 获取还款计划信息（使用内部方法避免递归）
        let repayment: Option<Repayment> =
            Database::exec_get_by_id(Self::ENTITY, &repayment_id).await?;
        // let repayment = Self::get_by_id_internal(repayment_id).await?;
        if repayment.is_none() {
            return Err(anyhow!("Repayment not found: {}", repayment_id).into());
        }
        let repayment = repayment.unwrap();

        Self::period_calc_internal(&repayment).await
    }

    /// 计算还款计划的利润、违约金等信息（使用统一时间戳）
    ///
    /// # 参数
    /// * `repayment` - 还款计划对象
    /// * `execution_date` - 统一的执行日期（只包含年月日）
    /// * `execution_timestamp` - 统一的执行时间戳
    ///
    /// # 返回值
    /// * `AppResult<(Decimal, Decimal, Decimal)>` - 返回 (利润金额, 违约金金额, 总应还金额)
    ///
    /// # 功能
    /// - 基于传入的还款计划对象进行计算，避免重复数据库查询
    /// - 使用 update_time 字段进行增量计算
    /// - 遵循 CalcPeriod 的计算周期规则
    /// - 使用统一时间戳避免计算过程中的时间切换问题
    async fn period_calc_internal_with_time(
        repayment: &Repayment,
        execution_date: NaiveDate,
        _execution_timestamp: i64,
    ) -> AppResult<(Decimal, Decimal, Decimal)> {
        // 使用传入的统一日期，避免时间切换问题
        let current_date = execution_date;

        // 获取剩余本金作为计算基数
        let principal_remain = if let Some(principal_str) = &repayment.principal_remain {
            Decimal::from_str(principal_str).unwrap_or(Decimal::ZERO)
        } else {
            Decimal::ZERO
        };

        // 获取剩余应还金额
        let amount_remain = if let Some(amount_str) = &repayment.amount_remain {
            Decimal::from_str(amount_str).unwrap_or(Decimal::ZERO)
        } else {
            Decimal::ZERO
        };

        // 如果没有剩余本金，直接返回零值
        if principal_remain == Decimal::ZERO {
            return Ok((Decimal::ZERO, Decimal::ZERO, Decimal::ZERO));
        }

        // 计算利润（基于 update_time 的增量计算）
        let profit_count =
            Self::calculate_profit_with_update_time(&repayment, principal_remain, current_date)?;

        // 计算违约金（如果当前时间大于 end_date 且 amount_remain > 0）
        let penalty_count =
            Self::calculate_penalty_with_update_time(&repayment, amount_remain, current_date)?;

        // 计算总应还金额
        let total_amount = principal_remain + profit_count + penalty_count;

        Ok((profit_count, penalty_count, total_amount))
    }

    /// 计算还款计划的利润、违约金等信息（内部方法，兼容性保留）
    ///
    /// # 参数
    /// * `repayment` - 还款计划对象
    ///
    /// # 返回值
    /// * `AppResult<(Decimal, Decimal, Decimal)>` - 返回 (利润金额, 违约金金额, 总应还金额)
    ///
    /// # 功能
    /// - 兼容性方法，内部调用带时间戳的版本
    /// - 使用当前时间作为执行时间
    async fn period_calc_internal(repayment: &Repayment) -> AppResult<(Decimal, Decimal, Decimal)> {
        let execution_timestamp = Local::now().timestamp_millis();
        let execution_date = chrono::DateTime::from_timestamp_millis(execution_timestamp)
            .ok_or_else(|| anyhow!("Invalid execution timestamp: {}", execution_timestamp))?
            .date_naive();

        Self::period_calc_internal_with_time(repayment, execution_date, execution_timestamp).await
    }

    /// 基于 update_time 计算利润金额（增量计算）
    ///
    /// # 参数
    /// * `repayment` - 还款计划对象
    /// * `principal_remain` - 剩余本金
    /// * `current_date` - 当前日期
    ///
    /// # 返回值
    /// * `AppResult<Decimal>` - 利润金额
    ///
    /// # 计算逻辑
    /// 1. 如果当前日期 <= begin_date，不计算
    /// 2. 如果 update_time 不存在，使用 current_date - begin_date
    /// 3. 如果 current_date == update_time（按天比较），不计算
    /// 4. 如果 current_date > update_time，计算两者差值
    /// 5. 遵循 CalcPeriod 的计算周期规则
    fn calculate_profit_with_update_time(
        repayment: &Repayment,
        principal_remain: Decimal,
        current_date: NaiveDate,
    ) -> AppResult<Decimal> {
        // 如果计算周期是 ONCE，不自动计算，使用已录入的数据
        if let Some(CalcPeriod::ONCE) = &repayment.profit_calc_period {
            if let Some(profit_str) = &repayment.profit_predict {
                return Ok(Decimal::from_str(profit_str).unwrap_or(Decimal::ZERO));
            }
            return Ok(Decimal::ZERO);
        }

        // 获取开始日期
        let begin_date = if let Some(begin_str) = &repayment.begin_date {
            match NaiveDate::parse_from_str(begin_str, "%Y-%m-%d") {
                Ok(date) => date,
                Err(_) => return Err(anyhow!("Invalid begin_date format: {}", begin_str).into()),
            }
        } else {
            return Ok(Decimal::ZERO); // 没有开始日期，无法计算
        };

        // 如果当前日期小于等于开始日期，不计算
        if current_date <= begin_date {
            return Ok(Decimal::ZERO);
        }

        // 确定计算的起始日期
        let calc_start_date = if let Some(update_time_millis) = repayment.update_time {
            // 将时间戳转换为日期（只保留年月日）
            let update_datetime = chrono::DateTime::from_timestamp_millis(update_time_millis)
                .ok_or_else(|| anyhow!("Invalid update_time timestamp: {}", update_time_millis))?;
            let update_date = update_datetime.date_naive();

            // 如果当前日期等于更新日期，不计算
            if current_date == update_date {
                return Ok(Decimal::ZERO);
            }

            // 如果当前日期大于更新日期，从更新日期的下一天开始计算
            if current_date > update_date {
                update_date + chrono::Duration::days(1)
            } else {
                // 当前日期小于更新日期，不应该发生，但为了安全返回零
                return Ok(Decimal::ZERO);
            }
        } else {
            // update_time 不存在，从 begin_date 开始计算
            begin_date
        };

        // 计算时间差
        let days_diff = current_date
            .signed_duration_since(calc_start_date)
            .num_days();
        if days_diff <= 0 {
            return Ok(Decimal::ZERO);
        }

        // 年化利率
        let annual_rate = repayment.profit_calc_fee;

        // 根据计算周期进行计算
        match &repayment.profit_calc_period {
            Some(CalcPeriod::DAY) => {
                // 按日利率计算：年化利率 / 365 * 天数 * 本金
                let daily_rate = annual_rate / Decimal::from(365);
                Ok(daily_rate * Decimal::from(days_diff) * principal_remain)
            }
            Some(CalcPeriod::MONTH) => {
                // 按月计算：只有满足整月才计算
                let months_diff = Self::calculate_months_diff(calc_start_date, current_date);
                if months_diff > 0 {
                    let monthly_rate = annual_rate / Decimal::from(12);
                    Ok(monthly_rate * Decimal::from(months_diff) * principal_remain)
                } else {
                    Ok(Decimal::ZERO)
                }
            }
            Some(CalcPeriod::QUARTER) => {
                // 按季度计算：只有满足整季度才计算
                let quarters_diff = Self::calculate_quarters_diff(calc_start_date, current_date);
                if quarters_diff > 0 {
                    let quarterly_rate = annual_rate / Decimal::from(4);
                    Ok(quarterly_rate * Decimal::from(quarters_diff) * principal_remain)
                } else {
                    Ok(Decimal::ZERO)
                }
            }
            Some(CalcPeriod::YEAR) => {
                // 按年计算：只有满足整年才计算
                let years_diff = Self::calculate_years_diff(calc_start_date, current_date);
                if years_diff > 0 {
                    Ok(annual_rate * Decimal::from(years_diff) * principal_remain)
                } else {
                    Ok(Decimal::ZERO)
                }
            }
            _ => Ok(Decimal::ZERO), // 其他情况或未设置周期
        }
    }

    /// 计算利润金额（原始方法，保留用于兼容性）
    ///
    /// # 参数
    /// * `repayment` - 还款计划对象
    /// * `principal_remain` - 剩余本金
    /// * `current_date` - 当前日期
    ///
    /// # 返回值
    /// * `AppResult<Decimal>` - 利润金额
    fn calculate_profit(
        repayment: &Repayment,
        principal_remain: Decimal,
        current_date: NaiveDate,
    ) -> AppResult<Decimal> {
        // 如果计算周期是 ONCE，不自动计算，使用已录入的数据
        if let Some(CalcPeriod::ONCE) = &repayment.profit_calc_period {
            if let Some(profit_str) = &repayment.profit_predict {
                return Ok(Decimal::from_str(profit_str).unwrap_or(Decimal::ZERO));
            }
            return Ok(Decimal::ZERO);
        }

        // 获取开始日期
        let begin_date = if let Some(begin_str) = &repayment.begin_date {
            match NaiveDate::parse_from_str(begin_str, "%Y-%m-%d") {
                Ok(date) => date,
                Err(_) => return Err(anyhow!("Invalid begin_date format: {}", begin_str).into()),
            }
        } else {
            return Ok(Decimal::ZERO); // 没有开始日期，无法计算
        };

        // 计算日期差值
        let days_diff = current_date.signed_duration_since(begin_date).num_days();
        if days_diff <= 0 {
            return Ok(Decimal::ZERO); // 还未到开始日期
        }

        // 年化利率
        let annual_rate = repayment.profit_calc_fee;

        match &repayment.profit_calc_period {
            Some(CalcPeriod::DAY) => {
                // 按日利率计算：年化利率 / 365 * 天数 * 本金
                let daily_rate = annual_rate / Decimal::from(365);
                Ok(daily_rate * Decimal::from(days_diff) * principal_remain)
            }
            Some(CalcPeriod::MONTH) => {
                // 按月计算：只有满足整月才计算
                let months_diff = Self::calculate_months_diff(begin_date, current_date);
                if months_diff > 0 {
                    let monthly_rate = annual_rate / Decimal::from(12);
                    Ok(monthly_rate * Decimal::from(months_diff) * principal_remain)
                } else {
                    Ok(Decimal::ZERO)
                }
            }
            Some(CalcPeriod::QUARTER) => {
                // 按季度计算：只有满足整季度才计算
                let quarters_diff = Self::calculate_quarters_diff(begin_date, current_date);
                if quarters_diff > 0 {
                    let quarterly_rate = annual_rate / Decimal::from(4);
                    Ok(quarterly_rate * Decimal::from(quarters_diff) * principal_remain)
                } else {
                    Ok(Decimal::ZERO)
                }
            }
            Some(CalcPeriod::YEAR) => {
                // 按年计算：只有满足整年才计算
                let years_diff = Self::calculate_years_diff(begin_date, current_date);
                if years_diff > 0 {
                    Ok(annual_rate * Decimal::from(years_diff) * principal_remain)
                } else {
                    Ok(Decimal::ZERO)
                }
            }
            _ => Ok(Decimal::ZERO), // 其他情况或未设置周期
        }
    }

    /// 基于 update_time 计算违约金金额（增量计算）
    ///
    /// # 参数
    /// * `repayment` - 还款计划对象
    /// * `amount_remain` - 剩余应还金额
    /// * `current_date` - 当前日期
    ///
    /// # 返回值
    /// * `AppResult<Decimal>` - 违约金金额
    ///
    /// # 计算逻辑
    /// 1. 如果当前时间 <= end_date，不计算违约金
    /// 2. 如果 amount_remain <= 0（已偿还完毕），不计算违约金
    /// 3. 如果当前时间 > end_date 且 amount_remain > 0，计算违约金
    /// 4. 基于 update_time 进行增量计算
    fn calculate_penalty_with_update_time(
        repayment: &Repayment,
        amount_remain: Decimal,
        current_date: NaiveDate,
    ) -> AppResult<Decimal> {
        // 如果剩余应还金额为零或负数，不计算违约金
        if amount_remain <= Decimal::ZERO {
            return Ok(Decimal::ZERO);
        }

        // 获取约定还款日期
        let end_date = if let Some(end_str) = &repayment.end_date {
            match NaiveDate::parse_from_str(end_str, "%Y-%m-%d") {
                Ok(date) => date,
                Err(_) => return Err(anyhow!("Invalid end_date format: {}", end_str).into()),
            }
        } else {
            return Ok(Decimal::ZERO); // 没有约定还款日期，无违约金
        };

        // 如果当前日期小于等于约定还款日期，不计算违约金
        if current_date <= end_date {
            return Ok(Decimal::ZERO);
        }

        // 确定违约金计算的起始日期
        let penalty_start_date = if let Some(update_time_millis) = repayment.update_time {
            // 将时间戳转换为日期（只保留年月日）
            let update_datetime = chrono::DateTime::from_timestamp_millis(update_time_millis)
                .ok_or_else(|| anyhow!("Invalid update_time timestamp: {}", update_time_millis))?;
            let update_date = update_datetime.date_naive();

            // 如果当前日期等于更新日期，不计算
            if current_date == update_date {
                return Ok(Decimal::ZERO);
            }

            // 违约金计算起始日期应该是 max(end_date + 1, update_date + 1)
            let penalty_base_date = end_date + chrono::Duration::days(1);
            let update_next_date = update_date + chrono::Duration::days(1);

            if update_next_date > penalty_base_date {
                update_next_date
            } else {
                penalty_base_date
            }
        } else {
            // update_time 不存在，从 end_date 的下一天开始计算违约金
            end_date + chrono::Duration::days(1)
        };

        // 计算逾期天数
        let overdue_days = current_date
            .signed_duration_since(penalty_start_date)
            .num_days();
        if overdue_days <= 0 {
            return Ok(Decimal::ZERO); // 未逾期或无新增逾期天数，无违约金
        }

        // penalty_calc_fee 是日息利率
        let daily_penalty_rate = repayment.penalty_calc_fee;

        // 违约金 = 日息利率 * 逾期天数 * 剩余应还金额
        Ok(daily_penalty_rate * Decimal::from(overdue_days) * amount_remain)
    }

    /// 计算违约金金额（原始方法，保留用于兼容性）
    ///
    /// # 参数
    /// * `repayment` - 还款计划对象
    /// * `principal_remain` - 剩余本金
    /// * `current_date` - 当前日期
    ///
    /// # 返回值
    /// * `AppResult<Decimal>` - 违约金金额
    fn calculate_penalty(
        repayment: &Repayment,
        principal_remain: Decimal,
        current_date: NaiveDate,
    ) -> AppResult<Decimal> {
        // 获取约定还款日期
        let end_date = if let Some(end_str) = &repayment.end_date {
            match NaiveDate::parse_from_str(end_str, "%Y-%m-%d") {
                Ok(date) => date,
                Err(_) => return Err(anyhow!("Invalid end_date format: {}", end_str).into()),
            }
        } else {
            return Ok(Decimal::ZERO); // 没有约定还款日期，无违约金
        };

        // 计算逾期天数
        let overdue_days = current_date.signed_duration_since(end_date).num_days();
        if overdue_days <= 0 {
            return Ok(Decimal::ZERO); // 未逾期，无违约金
        }

        // penalty_calc_fee 是日息利率
        let daily_penalty_rate = repayment.penalty_calc_fee;

        // 违约金 = 日息利率 * 逾期天数 * 剩余本金
        Ok(daily_penalty_rate * Decimal::from(overdue_days) * principal_remain)
    }

    /// 计算两个日期之间的月份差值（只计算完整月份）
    ///
    /// # 参数
    /// * `start_date` - 开始日期
    /// * `end_date` - 结束日期
    ///
    /// # 返回值
    /// * `i32` - 完整月份数
    fn calculate_months_diff(start_date: NaiveDate, end_date: NaiveDate) -> i32 {
        let start_year = start_date.year();
        let start_month = start_date.month() as i32;
        let end_year = end_date.year();
        let end_month = end_date.month() as i32;

        let mut months = (end_year - start_year) * 12 + (end_month - start_month);

        // 如果结束日期的天数小于开始日期的天数，说明这个月还没有完整过完
        if end_date.day() < start_date.day() {
            months -= 1;
        }

        months.max(0)
    }

    /// 计算两个日期之间的季度差值（只计算完整季度）
    ///
    /// # 参数
    /// * `start_date` - 开始日期
    /// * `end_date` - 结束日期
    ///
    /// # 返回值
    /// * `i32` - 完整季度数
    fn calculate_quarters_diff(start_date: NaiveDate, end_date: NaiveDate) -> i32 {
        let months_diff = Self::calculate_months_diff(start_date, end_date);
        months_diff / 3
    }

    /// 计算两个日期之间的年份差值（只计算完整年份）
    ///
    /// # 参数
    /// * `start_date` - 开始日期
    /// * `end_date` - 结束日期
    ///
    /// # 返回值
    /// * `i32` - 完整年份数
    fn calculate_years_diff(start_date: NaiveDate, end_date: NaiveDate) -> i32 {
        let mut years = end_date.year() - start_date.year();

        // 如果结束日期的月日小于开始日期的月日，说明这一年还没有完整过完
        if (end_date.month(), end_date.day()) < (start_date.month(), start_date.day()) {
            years -= 1;
        }

        years.max(0)
    }
}
