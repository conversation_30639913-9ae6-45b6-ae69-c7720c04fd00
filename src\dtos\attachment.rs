use crate::db::{Castable, Creatable, Patchable};
use crate::utils::date::{default_now, default_zero};
use salvo::prelude::{Extractible, ToSchema};
use serde::{Deserialize, Serialize};
use validator::Validate;

#[derive(Default, Clone, Deserialize, Serialize, Debug, Validate, ToSchema)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct AttachmentCreate {
    pub title: Option<String>,
    pub entity_type: String,
    pub file_type: Option<String>,
    pub save_dir: String,
    pub file_name: String,
    pub file_link: String,
    pub thumb_name: Option<String>,
    pub thumb_link: Option<String>,
    pub status: String,
    #[serde(default = "default_zero")]
    pub sort: i64,
    #[serde(default = "default_now")]
    pub created_at: i64,
    #[serde(default = "default_now")]
    pub updated_at: i64,
}

impl Creatable for AttachmentCreate {}

#[derive(<PERSON><PERSON><PERSON>, <PERSON>lone, Debug, Deserialize, Serialize, Extractible, ToSchema)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct AttachmentUpdate {
    #[salvo(extract(source(from = "param")))]
    pub id: String,
    pub title: Option<String>,
    pub entity_type: String,
    pub file_type: Option<String>,
    pub save_dir: String,
    pub file_name: String,
    pub file_link: String,
    pub thumb_name: Option<String>,
    pub thumb_link: Option<String>,
    pub status: String,
    #[serde(default = "default_zero")]
    pub sort: i64,
    #[serde(default = "default_now")]
    pub created_at: i64,
    #[serde(default = "default_now")]
    pub updated_at: i64,
}

impl Patchable for AttachmentUpdate {}

#[derive(Debug, Deserialize, Serialize, ToSchema, Default, Clone)]
pub struct AttachmentResponse {
    pub id: String,
    pub title: Option<String>,
    pub entity_type: String,
    pub file_type: Option<String>,
    pub save_dir: String,
    pub file_name: String,
    pub file_link: String,
    pub thumb_name: Option<String>,
    pub thumb_link: Option<String>,
    pub status: String,
    pub sort: i64,
    pub created_at: i64,
    pub updated_at: i64,
}

impl Castable for AttachmentResponse {}
