use crate::db::{Castable, Creatable, Patchable};
use crate::utils::date::default_now;
use salvo::prelude::{Extractible, ToSchema};
use serde::{Deserialize, Serialize};
use validator::Validate;

// 还款统计信息明细
#[derive(Default, Clone, Deserialize, Serialize, Debug, Validate, ToSchema)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct RepaymentLogCreate {
    pub parent_id: String,       // 还款计划ID
    pub total: String,           // 本次还款总额
    pub date: String,            // 本次还款日期
    pub profit: String,          // 还款的利息部分
    pub principal: String,       // 还款的本金部分
    pub status: String,          // 还款状态
    pub remark: Option<String>,  // 还款备注
    pub repayer: Option<String>, // 还款人
    #[serde(default = "default_now")]
    pub created_at: i64,
    #[serde(default = "default_now")]
    pub updated_at: i64,
}

impl Creatable for RepaymentLogCreate {}

#[derive(Default, Clone, Debug, Deserialize, Serialize, Extractible, ToSchema)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct RepaymentLogUpdate {
    #[salvo(extract(source(from = "param")))]
    pub id: String,
    pub parent_id: String,       // 还款计划ID
    pub total: String,           // 本次还款总额
    pub date: String,            // 本次还款日期
    pub profit: String,          // 还款的利息部分
    pub principal: String,       // 还款的本金部分
    pub status: String,          // 还款状态
    pub remark: Option<String>,  // 还款备注
    pub repayer: Option<String>, // 还款人
    #[serde(default = "default_now")]
    pub created_at: i64,
    #[serde(default = "default_now")]
    pub updated_at: i64,
}

impl Patchable for RepaymentLogUpdate {}

#[derive(Debug, Deserialize, Serialize, ToSchema, Default, Clone)]
pub struct RepaymentLogResponse {
    pub id: String,
    pub parent_id: String,       // 还款计划ID
    pub total: String,           // 本次还款总额
    pub date: String,            // 本次还款日期
    pub profit: String,          // 还款的利息部分
    pub principal: String,       // 还款的本金部分
    pub status: String,          // 还款状态
    pub remark: Option<String>,  // 还款备注
    pub repayer: Option<String>, // 还款人
    pub created_at: i64,
    pub updated_at: i64,
}

impl Castable for RepaymentLogResponse {}
