use crate::{
    app_writer::{App<PERSON><PERSON><PERSON>, AppWriter},
    db::{ListOptions, ListParams, ListResponse},
    dtos::repayment_log::{RepaymentLogCreate, RepaymentLogResponse, RepaymentLogUpdate},
    services::repayment_log::RepaymentLogService,
};
use salvo::{
    oapi::{
        endpoint,
        extract::{JsonBody, PathParam},
    },
    Request,
};
use salvo::{Router, Writer};

pub fn router() -> Router {
    Router::with_path("repayment_log")
        .post(create_repayment_log)
        .put(update_repayment_log)
        .push(
            Router::with_path("<id>")
                .get(get_repayment_log_by_id)
                .delete(delete_repayment_log),
        )
        .push(Router::with_path("list").post(get_repayment_log_list))
}

#[endpoint(tags("repayment_log"))]
async fn get_repayment_log_list(
    req: JsonBody<ListParams>,
) -> AppWriter<ListResponse<RepaymentLogResponse>> {
    let list = RepaymentLogService::get_list(req.0.clone()).await.unwrap();

    let mut data: Vec<RepaymentLogResponse> = Vec::new();
    for item in list {
        let tmp = item.response();
        data.push(tmp);
    }
    let page = req.0.page.clone().unwrap();
    let total = RepaymentLogService::get_total(req.params.clone())
        .await
        .unwrap();
    let res = ListResponse {
        data,
        total,
        page: page.page,
        size: page.limit,
    };

    AppWriter(Ok(res))
}

#[endpoint(tags("repayment_log"), parameters(("id", description = "repayment_log id for params")))]
async fn get_repayment_log_by_id(id: PathParam<String>) -> AppWriter<RepaymentLogResponse> {
    match RepaymentLogService::get_by_id(id.0).await {
        Ok(repayment_log) => {
            let res = repayment_log.response();
            AppWriter(Ok(res))
        }
        Err(e) => AppWriter(Err(e)),
    }
}

#[endpoint(tags("repayment_log"))]
async fn create_repayment_log(req: JsonBody<RepaymentLogCreate>) -> AppWriter<String> {
    let result = RepaymentLogService::create(req.0).await;
    AppWriter(result)
}

#[endpoint(tags("repayment_log"))]
async fn update_repayment_log(req: JsonBody<RepaymentLogUpdate>) -> AppResult<AppWriter<String>> {
    let result = RepaymentLogService::update(req.0).await;
    Ok(AppWriter(result))
}

#[endpoint(tags("repayment_log"), parameters(("id", description = "repayment_log id")))]
async fn delete_repayment_log(id: PathParam<String>) -> AppWriter<String> {
    let result = RepaymentLogService::delete(id.0).await;
    AppWriter(result)
}