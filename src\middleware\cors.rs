use crate::config::CFG;
use salvo::{
    cors::{AllowMethods, Co<PERSON>, Cors<PERSON>andler},
    http::Method,
};
// use std::time::Duration;

pub fn cors_middleware() -> CorsHandler {
    let cors_handler = Cors::new()
        .allow_origin(&CFG.server.cors_allow_origin)
        .allow_methods(vec![
            Method::OPTIONS,
            Method::GET,
            Method::PATCH,
            Method::POST,
            Method::PUT,
            Method::DELETE,
        ])
        .allow_headers(vec![
            "content-type",
            "authorization",
            "Content-Type",
            "Authorization",
            "origin",
            "accept",
            "x-requested-with",
        ])
        .allow_credentials(true)
        .max_age(3600)
        .into_handler();
    cors_handler
}

// 生成一个跨域中间件，但是对任何域名、端口、协议都允许
// pub fn cors_middleware() -> CorsHandler {
//     let cors_handler = Cors::new()
//         .allow_origin("*")
//         .allow_methods(AllowMethods::list(vec![
//             Method::GET,
//             Method::POST,
//             Method::PUT,
//             Method::DELETE,
//             Method::OPTIONS,
//         ]))
//         .allow_headers(AllowHeaders::any())
//         // .allow_credentials(true)
//         .max_age(3600)
//         .into_handler();
//     cors_handler
// }

// pub fn cors_middleware() -> CorsHandler {
//     let cors_handler = Cors::new()
//         .allow_origin("http://127.0.0.1:8080")
//         .allow_methods(AllowMethods::list(vec![
//             Method::GET,
//             Method::POST,
//             Method::PUT,
//             Method::DELETE,
//             Method::OPTIONS,
//         ]))
//         .allow_headers(vec![
//             "content-type",
//             "authorization",
//             "origin",
//             "accept",
//             "x-requested-with",
//         ])
//         .allow_credentials(true)
//         .expose_headers(vec!["content-type", "authorization"])
//         .max_age(3600)
//         .before(|req| {
//             log::info!("CORS Request: Method={}, Headers={:?}", req.method(), req.headers());
//             Ok(())
//         })
//         .into_handler();
//     cors_handler
// }
